package middleware

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/app"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/alarm"
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
	"github.com/gin-gonic/gin"
	"github.com/sony/gobreaker"
	"log"
	"net/http"
)

func CircuitBreakerMiddleware(manager *utils.CircuitBreakerManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取路由名称
		route := c.FullPath()
		if route == "" {
			route = "global"
		}

		// 获取路由对应的熔断器
		breaker, exists := manager.Breakers[route]
		if !exists {
			// 如果该路由没有配置熔断器，使用全局熔断器或者跳过熔断逻辑
			globalBreaker, hasGlobal := manager.Breakers["global"]
			if !hasGlobal {
				// 没有全局熔断器，直接处理请求
				c.Next()
				return
			}
			breaker = globalBreaker
		}

		// 检查熔断器状态
		if breaker.State() == gobreaker.StateOpen {
			log.Printf("Circuit breaker for route %s is OPEN, rejecting request", route)
			app.SetErrorBreaker(c, "服务暂不可用", 503)
			alarm.DingDingNormal(route+"服务触发熔断, 请尽快排查问题", "服务触发熔断")
			c.Abort()
			return
		}

		// 使用熔断器执行请求逻辑
		_, err := breaker.Execute(func() (interface{}, error) {
			// 处理正常的请求逻辑
			c.Next()
			
			// 检查响应状态码
			if c.Writer.Status() >= http.StatusInternalServerError {
				// 将服务端错误标记为失败
				return nil, http.ErrHandlerTimeout
			}
			return nil, nil
		})

		// 处理熔断器可能返回的错误
		if err != nil && !c.IsAborted() {
			// 如果是熔断器本身返回的错误（而不是业务逻辑中的错误）
			if err == gobreaker.ErrOpenState || err == gobreaker.ErrTooManyRequests {
				log.Printf("Circuit breaker error for route %s: %v", route, err)
				app.SetErrorBreaker(c, "服务暂不可用", 503)
				c.Abort()
			}
		}
	}
}
