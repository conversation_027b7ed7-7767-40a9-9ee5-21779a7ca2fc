package examples

import (
	"time"

	"gitee.com/jianmengkeji/wego-common-pkg/src/middleware"
	"github.com/gin-gonic/gin"
)

// SetupHealthCheckRoutes 设置健康检查路由示例
func SetupHealthCheckRoutes(r *gin.Engine) {
	// 初始化健康检查管理器
	config := &middleware.HealthCheckConfig{
		UseRedis:     true,               // 使用Redis存储状态
		ServerID:     "server-001",       // 服务器唯一标识
		RedisKey:     "app_health_check", // Redis存储键
		GracePeriod:  30 * time.Second,   // 优雅下线等待30秒
		DefaultState: true,               // 默认健康状态为true
	}

	middleware.InitHealthCheckManager(config)

	// 注册健康检查中间件
	r.Use(middleware.HealthCheckMiddleware())
	r.Use(middleware.HealthControlMiddleware())

	// 业务路由组
	api := r.Group("/api/v1")
	{
		api.GET("/users", func(c *gin.Context) {
			c.JSON(200, gin.H{"message": "用户列表"})
		})

		api.GET("/orders", func(c *gin.Context) {
			c.JSON(200, gin.H{"message": "订单列表"})
		})
	}
}

// 使用示例说明：
//
// 1. 健康检查接口：
//    GET /health 或 GET /health/check
//    - 返回200表示服务健康
//    - 返回503表示服务不健康
//
// 2. 健康状态控制接口：
//    GET /health/status   - 查看详细健康状态
//    POST /health/enable  - 启用健康检查（返回200）
//    POST /health/disable - 禁用健康检查（返回503）
//    POST /health/toggle  - 切换健康检查状态
//
// 3. 部署流程示例：
//    步骤1: 禁用服务器A的健康检查
//           curl -X POST http://server-a:8080/health/disable
//
//    步骤2: 等待负载均衡器将流量切换到服务器B
//           curl http://server-a:8080/health/status
//
//    步骤3: 部署服务器A的新代码
//           # 部署新版本代码
//
//    步骤4: 启用服务器A的健康检查
//           curl -X POST http://server-a:8080/health/enable
//
//    步骤5: 重复步骤1-4部署服务器B
//           curl -X POST http://server-b:8080/health/disable
//           # 部署服务器B
//           curl -X POST http://server-b:8080/health/enable
