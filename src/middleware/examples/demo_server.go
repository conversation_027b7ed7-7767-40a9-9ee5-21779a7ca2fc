package main

import (
	"log"
	"os"
	"time"

	"gitee.com/jianmengkeji/wego-common-pkg/src/middleware"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager"
	"github.com/gin-gonic/gin"
)

func main() {
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	// 创建Gin引擎
	r := gin.New()

	// 添加基本中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 获取服务器ID（从环境变量或使用默认值）
	serverID := os.Getenv("SERVER_ID")
	if serverID == "" {
		serverID = "demo-server-001"
	}

	// 获取端口（从环境变量或使用默认值）
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	// 初始化Redis（可选）
	useRedis := os.Getenv("USE_REDIS") == "true"
	if useRedis {
		redisConfig := &redisManager.RedisConfig{
			Host:     getEnv("REDIS_HOST", "localhost"),
			Port:     getEnv("REDIS_PORT", "6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
			Db:       0,
			Prefix:   "health_demo_",
		}
		redisManager.InitRedis(redisConfig)
		log.Printf("Redis已初始化: %s:%s", redisConfig.Host, redisConfig.Port)
	}

	// 初始化健康检查管理器
	healthConfig := &middleware.HealthCheckConfig{
		UseRedis:     useRedis,
		ServerID:     serverID,
		RedisKey:     "demo_health_check",
		GracePeriod:  30 * time.Second,
		DefaultState: true,
	}

	middleware.InitHealthCheckManager(healthConfig)
	log.Printf("健康检查管理器已初始化: ServerID=%s, UseRedis=%v", serverID, useRedis)

	// 注册健康检查中间件
	r.Use(middleware.HealthCheckMiddleware())
	r.Use(middleware.HealthControlMiddleware())

	// 业务路由
	setupRoutes(r, serverID)

	// 启动服务器
	log.Printf("服务器启动中... ServerID: %s, Port: %s", serverID, port)
	if err := r.Run(":" + port); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}

// setupRoutes 设置业务路由
func setupRoutes(r *gin.Engine, serverID string) {
	// 根路径
	r.GET("/", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message":   "健康检查中间件演示服务",
			"server_id": serverID,
			"timestamp": time.Now().Format(time.RFC3339),
			"endpoints": map[string]string{
				"health_check":   "GET /health",
				"health_status":  "GET /health/status",
				"health_enable":  "POST /health/enable",
				"health_disable": "POST /health/disable",
				"health_toggle":  "POST /health/toggle",
				"api_info":       "GET /api/info",
				"api_users":      "GET /api/users",
				"simulate_work":  "GET /api/work",
			},
		})
	})

	// API路由组
	api := r.Group("/api")
	{
		// 服务信息
		api.GET("/info", func(c *gin.Context) {
			manager := middleware.GetHealthCheckManager()
			c.JSON(200, gin.H{
				"server_id": serverID,
				"version":   "1.0.0",
				"healthy":   manager.IsHealthy(),
				"timestamp": time.Now().Format(time.RFC3339),
				"uptime":    time.Since(time.Now()).String(),
			})
		})

		// 模拟用户列表
		api.GET("/users", func(c *gin.Context) {
			users := []map[string]interface{}{
				{"id": 1, "name": "张三", "email": "<EMAIL>"},
				{"id": 2, "name": "李四", "email": "<EMAIL>"},
				{"id": 3, "name": "王五", "email": "<EMAIL>"},
			}

			c.JSON(200, gin.H{
				"server_id": serverID,
				"data":      users,
				"count":     len(users),
				"timestamp": time.Now().Format(time.RFC3339),
			})
		})

		// 模拟工作负载
		api.GET("/work", func(c *gin.Context) {
			// 模拟一些工作
			workDuration := 2 * time.Second
			time.Sleep(workDuration)

			c.JSON(200, gin.H{
				"server_id":     serverID,
				"message":       "工作完成",
				"work_duration": workDuration.String(),
				"timestamp":     time.Now().Format(time.RFC3339),
			})
		})

		// 模拟错误
		api.GET("/error", func(c *gin.Context) {
			c.JSON(500, gin.H{
				"server_id": serverID,
				"error":     "模拟的服务器错误",
				"timestamp": time.Now().Format(time.RFC3339),
			})
		})
	}

	// 管理路由组
	admin := r.Group("/admin")
	{
		// 服务器状态
		admin.GET("/status", func(c *gin.Context) {
			manager := middleware.GetHealthCheckManager()
			status := manager.GetStatus()
			status["server_info"] = map[string]interface{}{
				"server_id": serverID,
				"version":   "1.0.0",
				"gin_mode":  gin.Mode(),
			}

			c.JSON(200, gin.H{
				"code":    200,
				"data":    status,
				"message": "success",
			})
		})

		// 模拟部署
		admin.POST("/deploy", func(c *gin.Context) {
			manager := middleware.GetHealthCheckManager()

			// 1. 设置为不健康
			manager.SetHealthy(false)
			log.Printf("[%s] 开始模拟部署，设置为不健康状态", serverID)

			// 2. 模拟部署时间
			deployTime := 10 * time.Second
			time.Sleep(deployTime)

			// 3. 恢复健康状态
			manager.SetHealthy(true)
			log.Printf("[%s] 模拟部署完成，恢复健康状态", serverID)

			c.JSON(200, gin.H{
				"server_id":   serverID,
				"message":     "模拟部署完成",
				"deploy_time": deployTime.String(),
				"healthy":     manager.IsHealthy(),
				"timestamp":   time.Now().Format(time.RFC3339),
			})
		})
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

/*
使用说明：

1. 基本启动：
   go run demo_server.go

2. 指定服务器ID：
   SERVER_ID=server-001 go run demo_server.go

3. 指定端口：
   PORT=8081 go run demo_server.go

4. 启用Redis：
   USE_REDIS=true REDIS_HOST=localhost go run demo_server.go

5. 完整配置：
   SERVER_ID=server-001 PORT=8080 USE_REDIS=true REDIS_HOST=localhost REDIS_PORT=6379 go run demo_server.go

测试命令：

# 检查服务健康
curl http://localhost:8080/health

# 查看详细状态
curl http://localhost:8080/health/status

# 禁用健康检查
curl -X POST http://localhost:8080/health/disable

# 启用健康检查
curl -X POST http://localhost:8080/health/enable

# 切换健康状态
curl -X POST http://localhost:8080/health/toggle

# 查看服务信息
curl http://localhost:8080/api/info

# 模拟部署
curl -X POST http://localhost:8080/admin/deploy

多服务器测试：

# 终端1 - 启动服务器A
SERVER_ID=server-A PORT=8080 go run demo_server.go

# 终端2 - 启动服务器B
SERVER_ID=server-B PORT=8081 go run demo_server.go

# 终端3 - 测试部署流程
curl -X POST http://localhost:8080/health/disable  # 禁用A
curl http://localhost:8080/health                  # 检查A状态
curl -X POST http://localhost:8080/health/enable   # 启用A

curl -X POST http://localhost:8081/health/disable  # 禁用B
curl http://localhost:8081/health                  # 检查B状态
curl -X POST http://localhost:8081/health/enable   # 启用B
*/
