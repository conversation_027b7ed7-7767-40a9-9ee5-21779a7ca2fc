package examples

import (
	"time"

	"gitee.com/jianmengkeji/wego-common-pkg/src/middleware"
	"github.com/gin-gonic/gin"
)

// IPAccessControlExample IP访问控制示例
func IPAccessControlExample() {
	r := gin.Default()

	// 示例1: 严格的IP访问控制 - 只允许特定IP
	strictConfig := &middleware.HealthCheckConfig{
		UseRedis:     false,
		ServerID:     "strict-server",
		RedisKey:     "strict_health",
		GracePeriod:  30 * time.Second,
		DefaultState: true,
		AllowedIPs: []string{
			"************", // 管理员工作站IP
			"************", // 备用管理员IP
			"************", // 运维服务器IP
		},
		AllowLocalhost: false, // 不允许本地访问
	}

	// 示例2: 宽松的IP访问控制 - 允许内网访问
	relaxedConfig := &middleware.HealthCheckConfig{
		UseRedis:     true,
		ServerID:     "relaxed-server",
		RedisKey:     "relaxed_health",
		GracePeriod:  30 * time.Second,
		DefaultState: true,
		AllowedIPs: []string{
			"***********/16", // 整个192.168网段
			"10.0.0.0/8",     // 整个10.x网段
			"**********/12",  // 私有网络B类
		},
		AllowLocalhost: true, // 允许本地访问
	}

	// 示例3: 混合访问控制 - 特定IP + 网段
	hybridConfig := &middleware.HealthCheckConfig{
		UseRedis:     true,
		ServerID:     "hybrid-server",
		RedisKey:     "hybrid_health",
		GracePeriod:  30 * time.Second,
		DefaultState: true,
		AllowedIPs: []string{
			"127.0.0.1",       // 本地回环
			"::1",             // IPv6本地回环
			"***********/24",  // 办公网络
			"**********/24",   // 管理网络
			"************0",   // VPN出口IP
			"************/24", // 运维网络段
		},
		AllowLocalhost: true,
	}

	// 示例4: 开发环境配置 - 允许所有本地访问
	devConfig := &middleware.HealthCheckConfig{
		UseRedis:     false,
		ServerID:     "dev-server",
		RedisKey:     "dev_health",
		GracePeriod:  10 * time.Second,
		DefaultState: true,
		AllowedIPs: []string{
			"0.0.0.0/0", // 允许所有IP（仅开发环境）
		},
		AllowLocalhost: true,
	}

	// 根据环境选择配置
	var config *middleware.HealthCheckConfig
	env := getEnv("ENVIRONMENT", "development")

	switch env {
	case "production":
		config = strictConfig
	case "staging":
		config = hybridConfig
	case "testing":
		config = relaxedConfig
	default: // development
		config = devConfig
	}

	// 初始化健康检查管理器
	middleware.InitHealthCheckManager(config)

	// 注册中间件
	r.Use(middleware.HealthCheckMiddleware())
	r.Use(middleware.HealthControlMiddleware())

	// 示例路由
	r.GET("/", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message":     "IP访问控制示例服务",
			"environment": env,
			"server_id":   config.ServerID,
			"endpoints": map[string]string{
				"health_check":   "GET /health",
				"health_status":  "GET /health/status",
				"health_enable":  "POST /health/enable (需要IP授权)",
				"health_disable": "POST /health/disable (需要IP授权)",
				"health_toggle":  "POST /health/toggle (需要IP授权)",
			},
			"ip_config": map[string]interface{}{
				"allowed_ips":     config.AllowedIPs,
				"allow_localhost": config.AllowLocalhost,
			},
		})
	})

	r.Run(":8080")
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	// 这里简化实现，实际应该使用os.Getenv
	return defaultValue
}

/*
IP访问控制配置说明：

1. AllowedIPs 支持的格式：
   - 单个IP: "*************"
   - CIDR网段: "***********/24"
   - IPv6地址: "::1", "2001:db8::/32"

2. 常用的私有网络段：
   - 10.0.0.0/8        (10.0.0.0 - **************)
   - **********/12     (********** - **************)
   - ***********/16    (*********** - ***************)

3. 特殊地址：
   - 127.0.0.1         本地回环地址
   - ::1               IPv6本地回环地址
   - ***********/16    链路本地地址

4. 安全建议：
   - 生产环境：只允许特定的管理IP
   - 测试环境：允许内网段访问
   - 开发环境：可以放宽限制

5. 配置示例：

   # 严格模式（生产环境）
   allowed_ips:
     - "************"     # 管理员IP
     - "************"     # 运维服务器
   allow_localhost: false

   # 宽松模式（开发环境）
   allowed_ips:
     - "***********/16"   # 整个内网
     - "10.0.0.0/8"       # 私有网络
   allow_localhost: true

6. 测试命令：

   # 从允许的IP测试
   curl -X POST http://server:8080/health/disable

   # 从不允许的IP测试（应该返回403）
   curl -X POST http://server:8080/health/disable
   # 响应: {"error":"Access denied","message":"Your IP address is not allowed..."}

7. 日志监控：
   - 成功访问会记录INFO级别日志
   - 拒绝访问会记录WARN级别日志
   - 包含客户端IP、请求路径等信息

8. 负载均衡器注意事项：
   - 确保X-Forwarded-For头正确传递
   - 配置X-Real-IP头
   - 考虑代理服务器的IP

9. 故障排除：
   - 检查客户端真实IP
   - 验证CIDR配置格式
   - 查看应用日志
   - 测试网络连通性

10. 高级配置：
    - 可以结合时间窗口限制
    - 可以添加用户认证
    - 可以集成外部IP白名单服务
*/
