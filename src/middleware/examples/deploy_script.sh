#!/bin/bash

# 无中断部署脚本示例
# 使用健康检查中间件实现零停机部署

set -e

# 配置变量
SERVER_A_URL="http://server-a:8080"
SERVER_B_URL="http://server-b:8080"
HEALTH_CHECK_INTERVAL=5
MAX_WAIT_TIME=60
DEPLOY_TIMEOUT=300

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 检查服务健康状态
check_health() {
    local server_url=$1
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$server_url/health" || echo "000")
    echo $response
}

# 等待服务健康
wait_for_health() {
    local server_url=$1
    local timeout=$2
    local elapsed=0
    
    log_info "等待 $server_url 服务健康..."
    
    while [ $elapsed -lt $timeout ]; do
        local status=$(check_health $server_url)
        if [ "$status" = "200" ]; then
            log_info "$server_url 服务已健康"
            return 0
        fi
        
        sleep $HEALTH_CHECK_INTERVAL
        elapsed=$((elapsed + HEALTH_CHECK_INTERVAL))
        log_info "等待中... ($elapsed/${timeout}s) 状态码: $status"
    done
    
    log_error "$server_url 服务在 $timeout 秒内未恢复健康"
    return 1
}

# 等待服务不健康
wait_for_unhealthy() {
    local server_url=$1
    local timeout=$2
    local elapsed=0
    
    log_info "等待 $server_url 服务变为不健康..."
    
    while [ $elapsed -lt $timeout ]; do
        local status=$(check_health $server_url)
        if [ "$status" != "200" ]; then
            log_info "$server_url 服务已变为不健康"
            return 0
        fi
        
        sleep $HEALTH_CHECK_INTERVAL
        elapsed=$((elapsed + HEALTH_CHECK_INTERVAL))
        log_info "等待中... ($elapsed/${timeout}s) 状态码: $status"
    done
    
    log_error "$server_url 服务在 $timeout 秒内未变为不健康"
    return 1
}

# 禁用服务健康检查
disable_health_check() {
    local server_url=$1
    log_info "禁用 $server_url 健康检查..."
    
    local response=$(curl -s -X POST "$server_url/health/disable" || echo "failed")
    if [[ $response == *"Health check disabled"* ]]; then
        log_info "$server_url 健康检查已禁用"
        return 0
    else
        log_error "禁用 $server_url 健康检查失败: $response"
        return 1
    fi
}

# 启用服务健康检查
enable_health_check() {
    local server_url=$1
    log_info "启用 $server_url 健康检查..."
    
    local response=$(curl -s -X POST "$server_url/health/enable" || echo "failed")
    if [[ $response == *"Health check enabled"* ]]; then
        log_info "$server_url 健康检查已启用"
        return 0
    else
        log_error "启用 $server_url 健康检查失败: $response"
        return 1
    fi
}

# 获取服务状态
get_service_status() {
    local server_url=$1
    log_info "获取 $server_url 服务状态..."
    
    local response=$(curl -s "$server_url/health/status" || echo "failed")
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
}

# 部署单个服务器
deploy_server() {
    local server_url=$1
    local server_name=$2
    
    log_info "开始部署 $server_name ($server_url)"
    
    # 1. 禁用健康检查
    if ! disable_health_check $server_url; then
        log_error "$server_name 禁用健康检查失败"
        return 1
    fi
    
    # 2. 等待服务变为不健康
    if ! wait_for_unhealthy $server_url $MAX_WAIT_TIME; then
        log_error "$server_name 未能及时变为不健康状态"
        return 1
    fi
    
    # 3. 执行部署（这里需要替换为实际的部署命令）
    log_info "执行 $server_name 部署..."
    
    # 示例部署命令（需要根据实际情况修改）
    # docker pull your-app:latest
    # docker stop your-app-container
    # docker rm your-app-container
    # docker run -d --name your-app-container -p 8080:8080 your-app:latest
    
    # 模拟部署时间
    sleep 10
    
    log_info "$server_name 部署完成"
    
    # 4. 等待服务启动并启用健康检查
    sleep 5  # 等待服务启动
    
    if ! enable_health_check $server_url; then
        log_error "$server_name 启用健康检查失败"
        return 1
    fi
    
    # 5. 等待服务恢复健康
    if ! wait_for_health $server_url $MAX_WAIT_TIME; then
        log_error "$server_name 部署后未能恢复健康"
        return 1
    fi
    
    log_info "$server_name 部署成功并已恢复健康"
    return 0
}

# 主部署流程
main() {
    log_info "开始无中断部署流程"
    
    # 检查初始状态
    log_info "检查服务初始状态..."
    log_info "Server A 状态:"
    get_service_status $SERVER_A_URL
    log_info "Server B 状态:"
    get_service_status $SERVER_B_URL
    
    # 确保至少有一个服务是健康的
    server_a_health=$(check_health $SERVER_A_URL)
    server_b_health=$(check_health $SERVER_B_URL)
    
    if [ "$server_a_health" != "200" ] && [ "$server_b_health" != "200" ]; then
        log_error "两个服务都不健康，无法进行部署"
        exit 1
    fi
    
    # 部署 Server A
    log_info "========== 部署 Server A =========="
    if ! deploy_server $SERVER_A_URL "Server A"; then
        log_error "Server A 部署失败"
        exit 1
    fi
    
    # 等待一段时间确保 Server A 稳定
    log_info "等待 Server A 稳定运行..."
    sleep 10
    
    # 部署 Server B
    log_info "========== 部署 Server B =========="
    if ! deploy_server $SERVER_B_URL "Server B"; then
        log_error "Server B 部署失败"
        exit 1
    fi
    
    # 最终状态检查
    log_info "========== 部署完成 =========="
    log_info "最终服务状态:"
    log_info "Server A 状态:"
    get_service_status $SERVER_A_URL
    log_info "Server B 状态:"
    get_service_status $SERVER_B_URL
    
    log_info "无中断部署流程完成！"
}

# 清理函数
cleanup() {
    log_warn "部署被中断，尝试恢复服务..."
    enable_health_check $SERVER_A_URL || true
    enable_health_check $SERVER_B_URL || true
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 检查依赖
if ! command -v curl &> /dev/null; then
    log_error "curl 命令未找到，请安装 curl"
    exit 1
fi

if ! command -v jq &> /dev/null; then
    log_warn "jq 命令未找到，JSON 输出将不会格式化"
fi

# 执行主流程
main "$@"
