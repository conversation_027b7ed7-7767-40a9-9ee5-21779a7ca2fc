package middleware

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/ulule/limiter/v3/drivers/middleware/stdlib"
)

func RateLimiterMiddleware(manager *utils.RouteLimiterManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取路由名称
		route := c.FullPath()
		if route == "" {
			route = "global"
		}

		// 获取对应限流器
		limiter := manager.GetLimiter(route)

		// 使用自定义的错误处理函数
		// 配置自定义错误处理函数
		middleware := stdlib.NewMiddleware(
			limiter,
			stdlib.WithLimitReachedHandler(func(w http.ResponseWriter, r *http.Request) {
				// 自定义错误处理：转换为 GIN 响应
				utils.CustomErrorHandler(c)
				c.Abort() // 确保 GIN 停止后续处理
			}),
		)

		httpHandler := middleware.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Pass request to the next handler
			c.Next()
		}))

		// 适配 gin 的上下文
		httpHandler.ServeHTTP(c.Writer, c.Request)
	}
}
