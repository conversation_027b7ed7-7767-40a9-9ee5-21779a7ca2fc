package middleware

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/alarm"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/helper"
	"github.com/gin-gonic/gin"
	"net/http"
)

func Recover() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if r := recover(); r != nil {
				msgContent := helper.ErrorToString(r)
				// 打印堆栈信息
				alarm.DingDingSystem(msgContent, msgContent)
				c.JSON(http.StatusBadGateway, gin.H{
					"code": "10000",
					"msg":  msgContent,
					"data": nil,
				})
				c.Abort()
			}
		}()
		c.Next()
	}
}
