package middleware

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
)

// 测试健康检查管理器初始化
func TestHealthCheckManagerInit(t *testing.T) {

	manager := InitHealthCheckManager("bench-server")

	if manager == nil {
		t.<PERSON>rror("健康检查管理器初始化失败")
	}

	if !manager.IsHealthy() {
		t.<PERSON><PERSON>r("默认健康状态应该为true")
	}

	if manager.serverID != "test-server" {
		t.<PERSON><PERSON><PERSON>("服务器ID不匹配，期望: test-server, 实际: %s", manager.serverID)
	}
}

// 测试健康状态设置
func TestSetHealthy(t *testing.T) {

	manager := InitHealthCheckManager("bench-server")

	// 测试设置为不健康
	manager.SetHealthy(false)
	if manager.IsHealthy() {
		t.Error("设置为不健康后，状态应该为false")
	}

	// 测试设置为健康
	manager.SetHealthy(true)
	if !manager.IsHealthy() {
		t.Error("设置为健康后，状态应该为true")
	}
}

// 测试健康检查中间件
func TestHealthCheckMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	InitHealthCheckManager("bench-server")

	router := gin.New()
	router.Use(HealthCheckMiddleware())

	// 测试健康状态
	t.Run("健康状态返回200", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/health", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("期望状态码200，实际: %d", w.Code)
		}

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		data, ok := response["data"].(map[string]interface{})
		if !ok {
			t.Error("响应数据格式错误")
		}

		if data["status"] != "healthy" {
			t.Errorf("期望状态为healthy，实际: %v", data["status"])
		}
	})

	// 测试不健康状态
	t.Run("不健康状态返回503", func(t *testing.T) {
		manager := GetHealthCheckManager()
		manager.SetHealthy(false)

		req, _ := http.NewRequest("GET", "/health", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusServiceUnavailable {
			t.Errorf("期望状态码503，实际: %d", w.Code)
		}

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		if response["status"] != "unhealthy" {
			t.Errorf("期望状态为unhealthy，实际: %v", response["status"])
		}
	})
}

// 测试健康控制中间件
func TestHealthControlMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	InitHealthCheckManager("bench-server")

	router := gin.New()
	router.Use(HealthControlMiddleware())

	// 测试状态查询
	t.Run("查询健康状态", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/health/status", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("期望状态码200，实际: %d", w.Code)
		}

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		data, ok := response["data"].(map[string]interface{})
		if !ok {
			t.Error("响应数据格式错误")
		}

		if data["healthy"] != true {
			t.Errorf("期望健康状态为true，实际: %v", data["healthy"])
		}
	})

	// 测试禁用健康检查
	t.Run("禁用健康检查", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/health/disable", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("期望状态码200，实际: %d", w.Code)
		}

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		data, ok := response["data"].(map[string]interface{})
		if !ok {
			t.Error("响应数据格式错误")
		}

		if data["status"] != "unhealthy" {
			t.Errorf("期望状态为unhealthy，实际: %v", data["status"])
		}

		// 验证管理器状态已更新
		manager := GetHealthCheckManager()
		if manager.IsHealthy() {
			t.Error("管理器状态应该为不健康")
		}
	})

	// 测试启用健康检查
	t.Run("启用健康检查", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/health/enable", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("期望状态码200，实际: %d", w.Code)
		}

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		data, ok := response["data"].(map[string]interface{})
		if !ok {
			t.Error("响应数据格式错误")
		}

		if data["status"] != "healthy" {
			t.Errorf("期望状态为healthy，实际: %v", data["status"])
		}

		// 验证管理器状态已更新
		manager := GetHealthCheckManager()
		if !manager.IsHealthy() {
			t.Error("管理器状态应该为健康")
		}
	})

	// 测试切换健康检查状态
	t.Run("切换健康检查状态", func(t *testing.T) {
		manager := GetHealthCheckManager()
		originalState := manager.IsHealthy()

		req, _ := http.NewRequest("POST", "/health/toggle", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("期望状态码200，实际: %d", w.Code)
		}

		// 验证状态已切换
		newState := manager.IsHealthy()
		if newState == originalState {
			t.Error("健康状态应该已切换")
		}
	})
}

// 测试获取状态信息
func TestGetStatus(t *testing.T) {

	manager := InitHealthCheckManager("bench-server")

	status := manager.GetStatus()

	// 验证状态信息包含必要字段
	requiredFields := []string{"healthy", "server_id", "last_update", "uptime", "use_redis"}
	for _, field := range requiredFields {
		if _, exists := status[field]; !exists {
			t.Errorf("状态信息缺少字段: %s", field)
		}
	}

	// 验证字段值
	if status["healthy"] != true {
		t.Errorf("期望健康状态为true，实际: %v", status["healthy"])
	}

	if status["server_id"] != "test-server" {
		t.Errorf("期望服务器ID为test-server，实际: %v", status["server_id"])
	}

	if status["use_redis"] != false {
		t.Errorf("期望use_redis为false，实际: %v", status["use_redis"])
	}
}

// 基准测试
func BenchmarkHealthCheckMiddleware(b *testing.B) {
	gin.SetMode(gin.TestMode)

	InitHealthCheckManager("bench-server")

	router := gin.New()
	router.Use(HealthCheckMiddleware())

	req, _ := http.NewRequest("GET", "/health", nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}

// 基准测试 - 状态查询
func BenchmarkHealthStatus(b *testing.B) {
	gin.SetMode(gin.TestMode)

	InitHealthCheckManager("bench-server")

	router := gin.New()
	router.Use(HealthControlMiddleware())

	req, _ := http.NewRequest("GET", "/health/status", nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}

// 并发测试
func TestConcurrentHealthCheck(t *testing.T) {

	manager := InitHealthCheckManager("bench-server")

	// 启动多个goroutine并发访问
	done := make(chan bool, 100)

	for i := 0; i < 100; i++ {
		go func(id int) {
			// 随机设置健康状态
			if id%2 == 0 {
				manager.SetHealthy(true)
			} else {
				manager.SetHealthy(false)
			}

			// 读取状态
			_ = manager.IsHealthy()
			_ = manager.GetStatus()

			done <- true
		}(i)
	}

	// 等待所有goroutine完成
	for i := 0; i < 100; i++ {
		<-done
	}

	// 验证管理器仍然可用
	status := manager.GetStatus()
	if status == nil {
		t.Error("并发访问后管理器状态异常")
	}
}
