# 健康检查中间件功能总结

## 概述

已成功创建了一个完整的健康检查中间件，支持动态控制服务健康状态，实现无中断部署。该中间件提供了丰富的功能和完善的文档。

## 核心功能

### 1. 健康检查管理器 (`HealthCheckManager`)

**主要特性：**
- ✅ 动态健康状态控制
- ✅ Redis状态持久化（可选）
- ✅ 优雅下线支持
- ✅ 并发安全
- ✅ 详细状态信息

**核心方法：**
```go
// 设置健康状态
SetHealthy(healthy bool)

// 获取健康状态
IsHealthy() bool

// 获取详细状态
GetStatus() map[string]interface{}

// 取消优雅下线
CancelGracefulShutdown()
```

### 2. 中间件组件

#### HealthCheckMiddleware
- 处理 `/health` 和 `/health/check` 路径
- 根据健康状态返回200或503状态码
- 提供标准化的健康检查响应

#### HealthControlMiddleware  
- 处理健康状态控制接口
- 支持启用、禁用、切换、查询操作
- 提供管理接口用于运维操作

### 3. 配置系统

```go
type HealthCheckConfig struct {
    UseRedis     bool          // 是否使用Redis存储状态
    ServerID     string        // 服务器唯一标识
    RedisKey     string        // Redis存储键前缀
    GracePeriod  time.Duration // 优雅下线等待时间
    DefaultState bool          // 默认健康状态
}
```

## API接口

### 健康检查接口

| 接口 | 方法 | 说明 | 响应状态码 |
|------|------|------|------------|
| `/health` | GET | 基本健康检查 | 200/503 |
| `/health/check` | GET | 健康检查（别名） | 200/503 |

### 控制接口

| 接口 | 方法 | 说明 | 功能 |
|------|------|------|------|
| `/health/status` | GET | 查询详细状态 | 获取完整状态信息 |
| `/health/enable` | POST | 启用健康检查 | 设置为健康状态 |
| `/health/disable` | POST | 禁用健康检查 | 设置为不健康状态 |
| `/health/toggle` | POST | 切换健康状态 | 在健康/不健康间切换 |

## 无中断部署方案

### 部署流程

```bash
# 1. 禁用服务器A健康检查
curl -X POST http://server-a:8080/health/disable

# 2. 等待负载均衡器切换流量
curl http://server-a:8080/health  # 确认返回503

# 3. 部署新代码到服务器A
# ... 执行部署操作 ...

# 4. 启用服务器A健康检查
curl -X POST http://server-a:8080/health/enable

# 5. 重复流程部署服务器B
curl -X POST http://server-b:8080/health/disable
# ... 部署服务器B ...
curl -X POST http://server-b:8080/health/enable
```

### 自动化部署脚本

提供了完整的Shell脚本 `deploy_script.sh`，支持：
- 自动化双服务器部署
- 健康状态监控
- 错误处理和回滚
- 详细的日志输出
- 部署状态验证

## 文件结构

```
src/middleware/
├── health_check.go                 # 核心实现
├── health_check_test.go            # 单元测试
├── examples/
│   ├── health_check_example.go     # 使用示例
│   ├── demo_server.go              # 演示服务器
│   └── deploy_script.sh            # 部署脚本
├── HEALTH_CHECK_README.md          # 详细使用指南
└── HEALTH_CHECK_SUMMARY.md         # 功能总结（本文件）
```

## 使用示例

### 基本集成

```go
package main

import (
    "time"
    "gitee.com/jianmengkeji/wego-common-pkg/src/middleware"
    "github.com/gin-gonic/gin"
)

func main() {
    r := gin.Default()
    
    // 初始化健康检查
    config := &middleware.HealthCheckConfig{
        UseRedis:     true,
        ServerID:     "server-001",
        RedisKey:     "app_health",
        GracePeriod:  30 * time.Second,
        DefaultState: true,
    }
    
    middleware.InitHealthCheckManager(config)
    
    // 注册中间件
    r.Use(middleware.HealthCheckMiddleware())
    r.Use(middleware.HealthControlMiddleware())
    
    // 业务路由
    r.GET("/api/users", func(c *gin.Context) {
        c.JSON(200, gin.H{"users": []string{"张三", "李四"}})
    })
    
    r.Run(":8080")
}
```

### 演示服务器

提供了完整的演示服务器 `demo_server.go`：

```bash
# 启动演示服务器
go run src/middleware/examples/demo_server.go

# 指定配置启动
SERVER_ID=server-001 PORT=8080 USE_REDIS=true go run demo_server.go
```

**演示功能：**
- 完整的健康检查接口
- 业务API模拟
- 管理接口
- 模拟部署功能
- 环境变量配置

## 测试覆盖

### 单元测试

- ✅ 管理器初始化测试
- ✅ 健康状态设置测试  
- ✅ 中间件功能测试
- ✅ 控制接口测试
- ✅ 并发安全测试
- ✅ 性能基准测试

### 集成测试

- ✅ HTTP接口测试
- ✅ 状态持久化测试
- ✅ 错误处理测试
- ✅ 边界条件测试

## 实际应用场景

### 1. 微服务架构
- 服务注册与发现
- 负载均衡健康检查
- 服务熔断与降级

### 2. 容器化部署
- Kubernetes健康检查
- Docker容器状态管理
- 滚动更新支持

### 3. 传统部署
- 双机热备
- 蓝绿部署
- 灰度发布

## 最佳实践

### 1. 配置建议
```go
// 生产环境配置
config := &middleware.HealthCheckConfig{
    UseRedis:     true,                    // 启用Redis持久化
    ServerID:     os.Getenv("SERVER_ID"),  // 从环境变量获取
    RedisKey:     "prod_health_check",     // 生产环境键前缀
    GracePeriod:  60 * time.Second,        // 生产环境更长等待时间
    DefaultState: true,                    // 默认健康
}
```

### 2. 监控集成
```go
// 监控健康状态变化
manager := middleware.GetHealthCheckManager()
go func() {
    ticker := time.NewTicker(30 * time.Second)
    for range ticker.C {
        if !manager.IsHealthy() {
            // 发送告警
            sendAlert("服务不健康", manager.GetStatus())
        }
    }
}()
```

### 3. 负载均衡器配置
```nginx
# Nginx配置示例
upstream backend {
    server server-a:8080;
    server server-b:8080;
}

location /health {
    proxy_pass http://backend;
    proxy_connect_timeout 1s;
    proxy_read_timeout 1s;
}
```

## 优势特点

1. **零停机部署**: 支持无中断的服务更新
2. **状态持久化**: Redis存储确保状态一致性
3. **优雅下线**: 支持等待期确保请求完成
4. **易于集成**: 标准的Gin中间件接口
5. **丰富接口**: 提供完整的管理和监控接口
6. **高性能**: 轻量级实现，低延迟响应
7. **并发安全**: 支持高并发访问
8. **完善文档**: 详细的使用指南和示例

## 总结

健康检查中间件提供了一个完整的解决方案，用于实现服务的无中断部署。通过动态控制健康检查状态，配合负载均衡器，可以实现真正的零停机部署。

该中间件具有以下特点：
- **功能完整**: 涵盖健康检查的所有需求
- **易于使用**: 简单的配置和集成方式
- **生产就绪**: 经过充分测试，支持生产环境使用
- **扩展性强**: 支持Redis持久化和集群部署
- **文档完善**: 提供详细的使用指南和示例代码

可以立即在生产环境中使用，有效解决服务部署过程中的中断问题。
