# 健康检查中间件使用指南

## 概述

健康检查中间件提供了动态控制服务健康状态的功能，支持无中断部署。通过HTTP接口可以动态调整健康检查返回的状态码，实现优雅的服务上下线。

## 功能特性

- ✅ **动态健康状态控制**: 通过HTTP接口动态启用/禁用健康检查
- ✅ **Redis状态同步**: 支持将健康状态存储到Redis，实现多实例状态同步
- ✅ **优雅下线**: 支持优雅下线等待期，确保正在处理的请求完成
- ✅ **详细状态信息**: 提供详细的服务状态信息查询
- ✅ **无中断部署**: 配合负载均衡器实现零停机部署
- ✅ **日志记录**: 完整的操作日志记录

## 快速开始

### 1. 基本使用

```go
package main

import (
    "time"
    "gitee.com/jianmengkeji/wego-common-pkg/src/middleware"
    "github.com/gin-gonic/gin"
)

func main() {
    r := gin.Default()
    
    // 初始化健康检查管理器
    config := &middleware.HealthCheckConfig{
        UseRedis:     true,                    // 使用Redis存储状态
        ServerID:     "server-001",            // 服务器唯一标识
        RedisKey:     "app_health_check",      // Redis存储键
        GracePeriod:  30 * time.Second,        // 优雅下线等待30秒
        DefaultState: true,                    // 默认健康状态
    }
    
    middleware.InitHealthCheckManager(config)
    
    // 注册中间件
    r.Use(middleware.HealthCheckMiddleware())
    r.Use(middleware.HealthControlMiddleware())
    
    // 业务路由
    r.GET("/api/users", func(c *gin.Context) {
        c.JSON(200, gin.H{"message": "用户列表"})
    })
    
    r.Run(":8080")
}
```

### 2. 配置说明

```go
type HealthCheckConfig struct {
    UseRedis     bool          // 是否使用Redis存储状态
    ServerID     string        // 服务器唯一标识
    RedisKey     string        // Redis存储键前缀
    GracePeriod  time.Duration // 优雅下线等待时间
    DefaultState bool          // 默认健康状态
}
```

## API接口

### 健康检查接口

#### GET /health 或 GET /health/check
检查服务健康状态

**响应:**
- `200 OK`: 服务健康
- `503 Service Unavailable`: 服务不健康

```json
// 健康状态响应
{
    "code": 200,
    "data": {
        "status": "healthy",
        "server_id": "server-001",
        "timestamp": "2023-12-01T10:00:00Z"
    },
    "message": "success"
}

// 不健康状态响应
{
    "status": "unhealthy",
    "server_id": "server-001", 
    "timestamp": "2023-12-01T10:00:00Z",
    "message": "Service is temporarily unavailable"
}
```

### 健康状态控制接口

#### GET /health/status
获取详细健康状态信息

**响应:**
```json
{
    "code": 200,
    "data": {
        "healthy": true,
        "server_id": "server-001",
        "last_update": "2023-12-01T10:00:00Z",
        "uptime": "2h30m15s",
        "use_redis": true,
        "redis_status": {
            "healthy": "true",
            "updated_at": "2023-12-01T10:00:00Z",
            "server_id": "server-001",
            "update_by": "health_check_middleware"
        }
    },
    "message": "success"
}
```

#### POST /health/enable
启用健康检查（设置为健康状态）

**响应:**
```json
{
    "code": 200,
    "data": {
        "message": "Health check enabled",
        "server_id": "server-001",
        "status": "healthy",
        "timestamp": "2023-12-01T10:00:00Z"
    },
    "message": "success"
}
```

#### POST /health/disable
禁用健康检查（设置为不健康状态）

**响应:**
```json
{
    "code": 200,
    "data": {
        "message": "Health check disabled",
        "server_id": "server-001",
        "status": "unhealthy",
        "timestamp": "2023-12-01T10:00:00Z"
    },
    "message": "success"
}
```

#### POST /health/toggle
切换健康检查状态

**响应:**
```json
{
    "code": 200,
    "data": {
        "message": "Health check status toggled",
        "server_id": "server-001",
        "status": "healthy",
        "timestamp": "2023-12-01T10:00:00Z"
    },
    "message": "success"
}
```

## 无中断部署流程

### 手动部署流程

假设有两台服务器 Server A 和 Server B：

#### 1. 部署 Server A

```bash
# 1. 禁用 Server A 健康检查
curl -X POST http://server-a:8080/health/disable

# 2. 等待负载均衡器将流量切换到 Server B
# 检查状态直到返回503
curl http://server-a:8080/health

# 3. 部署新代码到 Server A
# ... 执行部署操作 ...

# 4. 启用 Server A 健康检查
curl -X POST http://server-a:8080/health/enable

# 5. 验证 Server A 恢复健康
curl http://server-a:8080/health
```

#### 2. 部署 Server B

```bash
# 重复相同流程部署 Server B
curl -X POST http://server-b:8080/health/disable
# ... 部署操作 ...
curl -X POST http://server-b:8080/health/enable
```

### 自动化部署

使用提供的部署脚本：

```bash
# 设置服务器地址
export SERVER_A_URL="http://server-a:8080"
export SERVER_B_URL="http://server-b:8080"

# 执行自动化部署
./deploy_script.sh
```

## 最佳实践

### 1. 负载均衡器配置

确保负载均衡器配置了健康检查：

```nginx
# Nginx 配置示例
upstream backend {
    server server-a:8080;
    server server-b:8080;
}

# 健康检查配置
location /health {
    proxy_pass http://backend;
    proxy_connect_timeout 1s;
    proxy_read_timeout 1s;
}
```

### 2. 监控和告警

```go
// 监控健康状态变化
manager := middleware.GetHealthCheckManager()

// 定期检查状态
go func() {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()
    
    for range ticker.C {
        if !manager.IsHealthy() {
            // 发送告警
            log.Logger.Warn("服务处于不健康状态", 
                zap.String("server_id", manager.GetStatus()["server_id"].(string)))
        }
    }
}()
```

### 3. 优雅关闭

```go
// 在应用关闭时设置为不健康状态
func gracefulShutdown() {
    manager := middleware.GetHealthCheckManager()
    manager.SetHealthy(false)
    
    // 等待优雅下线期
    time.Sleep(30 * time.Second)
    
    // 关闭应用
    os.Exit(0)
}
```

## 故障排除

### 常见问题

1. **Redis连接失败**
   - 检查Redis服务是否运行
   - 验证Redis连接配置
   - 查看应用日志

2. **健康检查状态不同步**
   - 确认Redis配置正确
   - 检查服务器ID是否唯一
   - 验证Redis键名配置

3. **部署过程中服务中断**
   - 检查负载均衡器健康检查间隔
   - 增加优雅下线等待时间
   - 验证部署脚本逻辑

### 调试命令

```bash
# 检查服务状态
curl -s http://localhost:8080/health/status | jq '.'

# 查看Redis中的状态
redis-cli HGETALL "app_health_check:server-001"

# 测试健康检查切换
curl -X POST http://localhost:8080/health/disable
curl http://localhost:8080/health
curl -X POST http://localhost:8080/health/enable
```

## 注意事项

1. **服务器ID唯一性**: 确保每个服务实例的ServerID唯一
2. **Redis依赖**: 如果使用Redis存储状态，确保Redis服务可用
3. **优雅下线时间**: 根据业务请求处理时间合理设置GracePeriod
4. **负载均衡器配置**: 确保负载均衡器的健康检查间隔合理
5. **监控告警**: 建议配置监控告警，及时发现异常状态
