package middleware

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/app"
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/syyongx/php2go"
	"net/http"
)

const (
	EXPIRE_TIME         = 2 * 86400
	REFRESH_EXPIRE_TIME = 30 * 86400
	NO_UNAUTHORIZED     = 401000
	AUTH_EXPIRE         = 402000
)

// AuthMiddleware 用于验证JWT Token的GIN中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头中获取Authorization字段
		tokenString := c.GetHeader("token")
		if tokenString == "" {
			app.SetError(c, "无效的Token", http.StatusUnauthorized)
			c.Abort()
			return
		}

		// 解析Token
		token, err := utils.ParseToken(tokenString)
		if err != nil {
			app.SetError(c, "无效的Token", http.StatusUnauthorized)
			c.Abort()
			return
		}

		// 验证Token是否有效
		if !token.Valid {
			app.SetError(c, "Token已过期或无效", http.StatusUnauthorized)
			c.Abort()
			return
		}

		// 将解析后的Claims存储到上下文中，以便后续使用
		if claims, ok := token.Claims.(jwt.MapClaims); ok {
			time, ok := claims["time"].(float64)
			var intTime int64

			if ok {
				intTime = int64(time)
			} else {
				app.SetError(c, "无效的Token", http.StatusUnauthorized)
				c.Abort()
				return
			}

			if intTime <= 0 {
				app.SetError(c, "无效的Token", http.StatusUnauthorized)
				c.Abort()
				return
			}

			if (php2go.Time() - intTime) > EXPIRE_TIME {
				app.SetError(c, "登录已过期", NO_UNAUTHORIZED)
				c.Abort()
				return
			}

			// 将 claims 转换为 app.UserTokenInfo 类型
			userTokenInfo := app.UserTokenInfo{
				Time: intTime,
			}

			if id, ok := claims["id"].(float64); ok {
				userTokenInfo.UnifyID = int(id)
			}
			if phone, ok := claims["phone"].(string); ok {
				userTokenInfo.Phone = phone
			}
			if loginID, ok := claims["login_id"].(string); ok {
				userTokenInfo.LoginID = loginID
			}
			if isVisitor, ok := claims["is_visitor"].(bool); ok {
				userTokenInfo.IsVisitor = isVisitor
			}
			if clientType, ok := claims["client_type"].(float64); ok {
				userTokenInfo.ClientType = int(clientType)
			}
			if areaCode, ok := claims["areaCode"].(string); ok {
				userTokenInfo.AreaCode = areaCode
			}

			// 将 userTokenInfo 存储到上下文中
			c.Set("userTokenInfo", userTokenInfo)

		}

		c.Next()
	}
}
