package middleware

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
)

// 测试IP访问控制初始化
func TestIPAccessControlInit(t *testing.T) {
	config := &HealthCheckConfig{
		UseRedis:       false,
		ServerID:       "test-server",
		RedisKey:       "test_health",
		GracePeriod:    10 * time.Second,
		DefaultState:   true,
		AllowedIPs:     []string{"127.0.0.1", "***********/24"},
		AllowLocalhost: true,
	}

	manager := InitHealthCheckManager(config)

	if manager == nil {
		t.Error("健康检查管理器初始化失败")
	}

	if len(manager.allowedIPs) != 2 {
		t.<PERSON>rrorf("期望2个允许的IP，实际: %d", len(manager.allowedIPs))
	}

	if !manager.allowLocalhost {
		t.Error("应该允许本地访问")
	}
}

// 测试IP访问控制验证
func TestIPAccessValidation(t *testing.T) {
	config := &HealthCheckConfig{
		UseRedis:       false,
		ServerID:       "test-server",
		AllowedIPs:     []string{"*************", "10.0.0.0/8"},
		AllowLocalhost: true,
	}

	manager := InitHealthCheckManager(config)

	// 测试允许的单个IP
	if !manager.isIPAllowed("*************") {
		t.Error("************* 应该被允许")
	}

	// 测试不允许的IP
	if manager.isIPAllowed("***********") {
		t.Error("*********** 不应该被允许")
	}

	// 测试CIDR网段内的IP
	if !manager.isIPAllowed("********") {
		t.Error("******** 应该被允许（在10.0.0.0/8网段内）")
	}

	// 测试本地IP
	if !manager.isIPAllowed("127.0.0.1") {
		t.Error("127.0.0.1 应该被允许（本地访问）")
	}
}

// 测试CIDR匹配功能
func TestCIDRMatching(t *testing.T) {
	testCases := []struct {
		ip       string
		cidr     string
		expected bool
	}{
		{"*************", "***********/24", true},
		{"*************", "***********/24", false},
		{"********", "10.0.0.0/8", true},
		{"********", "10.0.0.0/8", false},
		{"**********", "**********/12", true},
		{"**********", "**********/12", false},
		{"127.0.0.1", "127.0.0.1", true},
		{"*********", "127.0.0.1", false},
	}

	for _, tc := range testCases {
		result := isCIDRMatch(tc.ip, tc.cidr)
		if result != tc.expected {
			t.Errorf("isCIDRMatch(%s, %s) = %v, 期望 %v", tc.ip, tc.cidr, result, tc.expected)
		}
	}
}

// 测试本地IP检测
func TestLocalIPDetection(t *testing.T) {
	localIPs := []string{
		"127.0.0.1",
		"********",
		"***********",
		"**********",
	}

	for _, ip := range localIPs {
		if !isLocalIP(ip) {
			t.Errorf("%s 应该被识别为本地IP", ip)
		}
	}

	publicIPs := []string{
		"*******",
		"***********",
		"************",
	}

	for _, ip := range publicIPs {
		if isLocalIP(ip) {
			t.Errorf("%s 不应该被识别为本地IP", ip)
		}
	}
}

// 测试健康控制接口的IP访问限制
func TestHealthControlIPRestriction(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 配置严格的IP访问控制
	config := &HealthCheckConfig{
		UseRedis:       false,
		ServerID:       "test-server",
		AllowedIPs:     []string{"*************"},
		AllowLocalhost: false,
	}
	InitHealthCheckManager(config)

	router := gin.New()
	router.Use(HealthControlMiddleware())

	// 测试被拒绝的IP访问
	t.Run("拒绝未授权IP访问", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/health/disable", nil)
		req.RemoteAddr = "***********:12345" // 模拟不允许的IP
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusForbidden {
			t.Errorf("期望状态码403，实际: %d", w.Code)
		}

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		if response["error"] != "Access denied" {
			t.Errorf("期望错误信息'Access denied'，实际: %v", response["error"])
		}
	})

	// 测试允许的IP访问
	t.Run("允许授权IP访问", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/health/disable", nil)
		req.RemoteAddr = "*************:12345" // 模拟允许的IP
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("期望状态码200，实际: %d", w.Code)
		}
	})
}

// 测试X-Forwarded-For头处理
func TestXForwardedForHandling(t *testing.T) {
	gin.SetMode(gin.TestMode)

	config := &HealthCheckConfig{
		UseRedis:       false,
		ServerID:       "test-server",
		AllowedIPs:     []string{"*************"},
		AllowLocalhost: false,
	}
	InitHealthCheckManager(config)

	router := gin.New()
	router.Use(HealthControlMiddleware())

	// 测试X-Forwarded-For头
	t.Run("处理X-Forwarded-For头", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/health/disable", nil)
		req.RemoteAddr = "********:12345"                  // 代理服务器IP
		req.Header.Set("X-Forwarded-For", "*************") // 真实客户端IP
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("期望状态码200，实际: %d", w.Code)
		}
	})

	// 测试X-Real-IP头
	t.Run("处理X-Real-IP头", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/health/disable", nil)
		req.RemoteAddr = "********:12345"            // 代理服务器IP
		req.Header.Set("X-Real-IP", "*************") // 真实客户端IP
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("期望状态码200，实际: %d", w.Code)
		}
	})
}

// 测试健康状态查询不受IP限制
func TestHealthStatusNoIPRestriction(t *testing.T) {
	gin.SetMode(gin.TestMode)

	config := &HealthCheckConfig{
		UseRedis:       false,
		ServerID:       "test-server",
		AllowedIPs:     []string{"*************"},
		AllowLocalhost: false,
	}
	InitHealthCheckManager(config)

	router := gin.New()
	router.Use(HealthControlMiddleware())

	// 健康状态查询应该不受IP限制
	t.Run("健康状态查询不受IP限制", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/health/status", nil)
		req.RemoteAddr = "***********:12345" // 不允许的IP
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("期望状态码200，实际: %d", w.Code)
		}
	})
}

// 基准测试IP访问控制性能
func BenchmarkIPAccessControl(b *testing.B) {
	config := &HealthCheckConfig{
		UseRedis:       false,
		ServerID:       "bench-server",
		AllowedIPs:     []string{"***********/24", "10.0.0.0/8", "**********/12"},
		AllowLocalhost: true,
	}

	manager := InitHealthCheckManager(config)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		manager.isIPAllowed("*************")
	}
}

// 基准测试CIDR匹配性能
func BenchmarkCIDRMatching(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		isCIDRMatch("*************", "***********/24")
	}
}

// 测试并发IP访问控制
func TestConcurrentIPAccessControl(t *testing.T) {
	config := &HealthCheckConfig{
		UseRedis:       false,
		ServerID:       "concurrent-server",
		AllowedIPs:     []string{"***********/24"},
		AllowLocalhost: true,
	}

	manager := InitHealthCheckManager(config)

	// 启动多个goroutine并发检查IP访问
	done := make(chan bool, 100)

	for i := 0; i < 100; i++ {
		go func(id int) {
			// 测试不同的IP
			testIPs := []string{
				"*************",
				"127.0.0.1",
				"***********",
				"********",
			}

			for _, ip := range testIPs {
				_ = manager.isIPAllowed(ip)
			}

			done <- true
		}(i)
	}

	// 等待所有goroutine完成
	for i := 0; i < 100; i++ {
		<-done
	}

	// 验证管理器仍然可用
	if !manager.isIPAllowed("127.0.0.1") {
		t.Error("并发访问后IP访问控制异常")
	}
}
