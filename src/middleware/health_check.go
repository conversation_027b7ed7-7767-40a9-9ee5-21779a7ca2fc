package middleware

import (
	"context"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"

	"gitee.com/jianmengkeji/wego-common-pkg/src/app"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/log"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// HealthCheckManager 健康检查管理器
type HealthCheckManager struct {
	mu             sync.RWMutex
	isHealthy      bool
	useRedis       bool
	redisKey       string
	serverID       string
	lastUpdate     time.Time
	gracePeriod    time.Duration // 优雅下线等待时间
	shutdownChan   chan struct{}
	allowedIPs     []string // 允许访问控制接口的IP列表
	allowLocalhost bool     // 是否允许本地访问
}

var (
	healthManager *HealthCheckManager
	once          sync.Once
)

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	UseRedis       bool          `yaml:"use_redis" json:"use_redis"`             // 是否使用Redis存储状态
	ServerID       string        `yaml:"server_id" json:"server_id"`             // 服务器唯一标识
	RedisKey       string        `yaml:"redis_key" json:"redis_key"`             // Redis存储键
	GracePeriod    time.Duration `yaml:"grace_period" json:"grace_period"`       // 优雅下线等待时间
	DefaultState   bool          `yaml:"default_state" json:"default_state"`     // 默认健康状态
	AllowedIPs     []string      `yaml:"allowed_ips" json:"allowed_ips"`         // 允许访问控制接口的IP列表
	AllowLocalhost bool          `yaml:"allow_localhost" json:"allow_localhost"` // 是否允许本地访问
}

// InitHealthCheckManager 初始化健康检查管理器
func InitHealthCheckManager(config *HealthCheckConfig) *HealthCheckManager {
	once.Do(func() {
		if config == nil {
			config = &HealthCheckConfig{
				UseRedis:       false,
				ServerID:       "default",
				RedisKey:       "health_check",
				GracePeriod:    30 * time.Second,
				DefaultState:   true,
				AllowedIPs:     []string{"127.0.0.1", "::1"},
				AllowLocalhost: true,
			}
		}

		// 如果没有配置允许的IP，默认只允许本地访问
		if len(config.AllowedIPs) == 0 && !config.AllowLocalhost {
			config.AllowedIPs = []string{"127.0.0.1", "::1"}
			config.AllowLocalhost = true
		}

		healthManager = &HealthCheckManager{
			isHealthy:      config.DefaultState,
			useRedis:       config.UseRedis,
			redisKey:       config.RedisKey,
			serverID:       config.ServerID,
			lastUpdate:     time.Now(),
			gracePeriod:    config.GracePeriod,
			shutdownChan:   make(chan struct{}),
			allowedIPs:     config.AllowedIPs,
			allowLocalhost: config.AllowLocalhost,
		}

		// 如果使用Redis，从Redis加载初始状态
		if healthManager.useRedis {
			healthManager.loadStateFromRedis()
		}

		log.AppLogger.Info("健康检查管理器初始化完成",
			zap.String("server_id", healthManager.serverID),
			zap.Bool("use_redis", healthManager.useRedis),
			zap.Bool("initial_state", healthManager.isHealthy),
			zap.Strings("allowed_ips", healthManager.allowedIPs),
			zap.Bool("allow_localhost", healthManager.allowLocalhost),
		)
	})

	return healthManager
}

// isIPAllowed 检查IP是否被允许访问控制接口
func (h *HealthCheckManager) isIPAllowed(clientIP string) bool {
	h.mu.RLock()
	defer h.mu.RUnlock()

	// 如果允许本地访问，检查是否为本地IP
	if h.allowLocalhost {
		if clientIP == "127.0.0.1" || clientIP == "::1" || clientIP == "localhost" {
			return true
		}
		// 检查是否为本地网络IP
		if isLocalIP(clientIP) {
			return true
		}
	}

	// 检查是否在允许的IP列表中
	for _, allowedIP := range h.allowedIPs {
		if clientIP == allowedIP {
			return true
		}
		// 支持CIDR格式的IP段匹配
		if isCIDRMatch(clientIP, allowedIP) {
			return true
		}
	}

	return false
}

// isLocalIP 检查是否为本地网络IP
func isLocalIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// 检查是否为回环地址
	if parsedIP.IsLoopback() {
		return true
	}

	// 检查是否为私有网络地址
	if parsedIP.IsPrivate() {
		return true
	}

	// 检查是否为链路本地地址
	if parsedIP.IsLinkLocalUnicast() {
		return true
	}

	return false
}

// isCIDRMatch 检查IP是否匹配CIDR格式的网络段
func isCIDRMatch(ip, cidr string) bool {
	// 如果不包含/，说明不是CIDR格式，直接比较
	if !strings.Contains(cidr, "/") {
		return ip == cidr
	}

	// 解析CIDR网络段
	_, network, err := net.ParseCIDR(cidr)
	if err != nil {
		// 如果解析失败，回退到字符串比较
		return ip == cidr
	}

	// 解析IP地址
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// 检查IP是否在网络段内
	return network.Contains(parsedIP)
}

// GetHealthCheckManager 获取健康检查管理器实例
func GetHealthCheckManager() *HealthCheckManager {
	if healthManager == nil {
		return InitHealthCheckManager(nil)
	}
	return healthManager
}

// loadStateFromRedis 从Redis加载健康状态
func (h *HealthCheckManager) loadStateFromRedis() {
	if !h.useRedis {
		return
	}

	ctx := context.Background()
	key := h.redisKey + ":" + h.serverID

	value, exists := redisManager.RedisManagerService.HGet(ctx, key, "healthy")
	if exists {
		if healthyStr, ok := value.(string); ok {
			h.isHealthy = healthyStr == "true"
			log.AppLogger.Info("从Redis加载健康状态",
				zap.String("server_id", h.serverID),
				zap.Bool("healthy", h.isHealthy),
			)
		}
	}
}

// saveStateToRedis 保存健康状态到Redis
func (h *HealthCheckManager) saveStateToRedis() {
	if !h.useRedis {
		return
	}

	ctx := context.Background()
	key := h.redisKey + ":" + h.serverID

	healthyStr := "false"
	if h.isHealthy {
		healthyStr = "true"
	}

	fields := map[string]interface{}{
		"healthy":    healthyStr,
		"updated_at": time.Now().Format(time.RFC3339),
		"server_id":  h.serverID,
		"update_by":  "health_check_middleware",
	}

	redisManager.RedisManagerService.HMSet(ctx, key, fields)
}

// SetHealthy 设置健康状态
func (h *HealthCheckManager) SetHealthy(healthy bool) {
	h.mu.Lock()
	defer h.mu.Unlock()

	oldState := h.isHealthy
	h.isHealthy = healthy
	h.lastUpdate = time.Now()

	// 保存到Redis
	h.saveStateToRedis()

	log.AppLogger.Info("健康状态已更新",
		zap.String("server_id", h.serverID),
		zap.Bool("old_state", oldState),
		zap.Bool("new_state", h.isHealthy),
		zap.Time("updated_at", h.lastUpdate),
	)

	// 如果设置为不健康，启动优雅下线流程
	if !healthy && oldState {
		go h.startGracefulShutdown()
	}
}

// IsHealthy 获取当前健康状态
func (h *HealthCheckManager) IsHealthy() bool {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return h.isHealthy
}

// GetStatus 获取详细状态信息
func (h *HealthCheckManager) GetStatus() map[string]interface{} {
	h.mu.RLock()
	defer h.mu.RUnlock()

	status := map[string]interface{}{
		"healthy":     h.isHealthy,
		"server_id":   h.serverID,
		"last_update": h.lastUpdate.Format(time.RFC3339),
		"uptime":      time.Since(h.lastUpdate).String(),
		"use_redis":   h.useRedis,
	}

	// 如果使用Redis，获取Redis中的状态
	if h.useRedis {
		ctx := context.Background()
		key := h.redisKey + ":" + h.serverID
		redisStatus := redisManager.RedisManagerService.HGetAll(ctx, key)
		if len(redisStatus) > 0 {
			status["redis_status"] = redisStatus
		}
	}

	return status
}

// startGracefulShutdown 启动优雅下线流程
func (h *HealthCheckManager) startGracefulShutdown() {
	log.AppLogger.Info("启动优雅下线流程",
		zap.String("server_id", h.serverID),
		zap.Duration("grace_period", h.gracePeriod),
	)

	select {
	case <-time.After(h.gracePeriod):
		log.AppLogger.Info("优雅下线等待期结束", zap.String("server_id", h.serverID))
	case <-h.shutdownChan:
		log.AppLogger.Info("优雅下线被中断", zap.String("server_id", h.serverID))
	}
}

// CancelGracefulShutdown 取消优雅下线
func (h *HealthCheckManager) CancelGracefulShutdown() {
	select {
	case h.shutdownChan <- struct{}{}:
		log.AppLogger.Info("优雅下线已取消", zap.String("server_id", h.serverID))
	default:
		// 通道已满或没有等待的goroutine
	}
}

// HealthCheckMiddleware 健康检查中间件
func HealthCheckMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只处理健康检查相关的路径
		if c.Request.URL.Path == "/health" || c.Request.URL.Path == "/health/check" {
			manager := GetHealthCheckManager()

			if manager.IsHealthy() {
				app.SetSuccess(c, map[string]interface{}{
					"status":    "healthy",
					"server_id": manager.serverID,
					"timestamp": time.Now().Format(time.RFC3339),
				})
			} else {
				c.JSON(http.StatusServiceUnavailable, gin.H{
					"status":    "unhealthy",
					"server_id": manager.serverID,
					"timestamp": time.Now().Format(time.RFC3339),
					"message":   "Service is temporarily unavailable",
				})
			}
			c.Abort()
			return
		}

		c.Next()
	}
}

// HealthControlMiddleware 健康状态控制中间件
func HealthControlMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path

		switch path {
		case "/health/status":
			handleHealthStatus(c)
		case "/health/enable":
			if !checkIPAccess(c) {
				return
			}
			handleHealthEnable(c)
		case "/health/disable":
			if !checkIPAccess(c) {
				return
			}
			handleHealthDisable(c)
		case "/health/toggle":
			if !checkIPAccess(c) {
				return
			}
			handleHealthToggle(c)
		default:
			c.Next()
			return
		}

		c.Abort()
	}
}

// checkIPAccess 检查IP访问权限
func checkIPAccess(c *gin.Context) bool {
	manager := GetHealthCheckManager()
	clientIP := getClientIP(c)

	if !manager.isIPAllowed(clientIP) {
		log.AppLogger.Warn("健康检查控制接口访问被拒绝",
			zap.String("client_ip", clientIP),
			zap.String("path", c.Request.URL.Path),
			zap.String("user_agent", c.GetHeader("User-Agent")),
		)

		c.JSON(http.StatusForbidden, gin.H{
			"error":     "Access denied",
			"message":   "Your IP address is not allowed to access this endpoint",
			"client_ip": clientIP,
			"timestamp": time.Now().Format(time.RFC3339),
		})
		c.Abort()
		return false
	}

	log.AppLogger.Info("健康检查控制接口访问允许",
		zap.String("client_ip", clientIP),
		zap.String("path", c.Request.URL.Path),
	)

	return true
}

// getClientIP 获取客户端真实IP地址
func getClientIP(c *gin.Context) string {
	// 优先从X-Forwarded-For头获取
	if xff := c.GetHeader("X-Forwarded-For"); xff != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		ips := strings.Split(xff, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}

	// 从X-Real-IP头获取
	if xri := c.GetHeader("X-Real-IP"); xri != "" {
		return strings.TrimSpace(xri)
	}

	// 从RemoteAddr获取
	if remoteAddr := c.Request.RemoteAddr; remoteAddr != "" {
		// RemoteAddr格式为 "IP:Port"，需要分离IP
		if idx := strings.LastIndex(remoteAddr, ":"); idx != -1 {
			return remoteAddr[:idx]
		}
		return remoteAddr
	}

	return "unknown"
}

// handleHealthStatus 处理健康状态查询
func handleHealthStatus(c *gin.Context) {
	manager := GetHealthCheckManager()
	status := manager.GetStatus()
	app.SetSuccess(c, status)
}

// handleHealthEnable 处理启用健康检查
func handleHealthEnable(c *gin.Context) {
	manager := GetHealthCheckManager()
	manager.SetHealthy(true)
	manager.CancelGracefulShutdown()

	app.SetSuccess(c, map[string]interface{}{
		"message":   "Health check enabled",
		"server_id": manager.serverID,
		"status":    "healthy",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleHealthDisable 处理禁用健康检查
func handleHealthDisable(c *gin.Context) {
	manager := GetHealthCheckManager()
	manager.SetHealthy(false)

	app.SetSuccess(c, map[string]interface{}{
		"message":   "Health check disabled",
		"server_id": manager.serverID,
		"status":    "unhealthy",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleHealthToggle 处理切换健康检查状态
func handleHealthToggle(c *gin.Context) {
	manager := GetHealthCheckManager()
	newState := !manager.IsHealthy()
	manager.SetHealthy(newState)

	if newState {
		manager.CancelGracefulShutdown()
	}

	status := "unhealthy"
	if newState {
		status = "healthy"
	}

	app.SetSuccess(c, map[string]interface{}{
		"message":   "Health check status toggled",
		"server_id": manager.serverID,
		"status":    status,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}
