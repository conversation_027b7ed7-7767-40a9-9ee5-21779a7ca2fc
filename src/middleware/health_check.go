package middleware

import (
	"context"
	"net/http"
	"sync"
	"time"

	"gitee.com/jianmengkeji/wego-common-pkg/src/app"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/log"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// HealthCheckManager 健康检查管理器
type HealthCheckManager struct {
	mu           sync.RWMutex
	isHealthy    bool
	useRedis     bool
	redisKey     string
	serverID     string
	lastUpdate   time.Time
	gracePeriod  time.Duration // 优雅下线等待时间
	shutdownChan chan struct{}
}

var (
	healthManager *HealthCheckManager
	once          sync.Once
)

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	UseRedis     bool          `yaml:"use_redis" json:"use_redis"`         // 是否使用Redis存储状态
	ServerID     string        `yaml:"server_id" json:"server_id"`         // 服务器唯一标识
	RedisKey     string        `yaml:"redis_key" json:"redis_key"`         // Redis存储键
	GracePeriod  time.Duration `yaml:"grace_period" json:"grace_period"`   // 优雅下线等待时间
	DefaultState bool          `yaml:"default_state" json:"default_state"` // 默认健康状态
}

// InitHealthCheckManager 初始化健康检查管理器
func InitHealthCheckManager(ServerId string) *HealthCheckManager {
	once.Do(func() {

		config := &HealthCheckConfig{
			UseRedis:     false,
			ServerID:     ServerId,
			RedisKey:     "health_check",
			GracePeriod:  30 * time.Second,
			DefaultState: true,
		}

		healthManager = &HealthCheckManager{
			isHealthy:    config.DefaultState,
			useRedis:     config.UseRedis,
			redisKey:     config.RedisKey,
			serverID:     config.ServerID,
			lastUpdate:   time.Now(),
			gracePeriod:  config.GracePeriod,
			shutdownChan: make(chan struct{}),
		}

		// 如果使用Redis，从Redis加载初始状态
		if healthManager.useRedis {
			healthManager.loadStateFromRedis()
		}

		log.AppLogger.Info("健康检查管理器初始化完成",
			zap.String("server_id", healthManager.serverID),
			zap.Bool("use_redis", healthManager.useRedis),
			zap.Bool("initial_state", healthManager.isHealthy),
		)
	})

	return healthManager
}

// GetHealthCheckManager 获取健康检查管理器实例
func GetHealthCheckManager() *HealthCheckManager {
	if healthManager == nil {
		return InitHealthCheckManager("")
	}
	return healthManager
}

// loadStateFromRedis 从Redis加载健康状态
func (h *HealthCheckManager) loadStateFromRedis() {
	if !h.useRedis {
		return
	}

	ctx := context.Background()
	key := h.redisKey + ":" + h.serverID

	value, exists := redisManager.RedisManagerService.HGet(ctx, key, "healthy")
	if exists {
		if healthyStr, ok := value.(string); ok {
			h.isHealthy = healthyStr == "true"
			log.AppLogger.Info("从Redis加载健康状态",
				zap.String("server_id", h.serverID),
				zap.Bool("healthy", h.isHealthy),
			)
		}
	}
}

// saveStateToRedis 保存健康状态到Redis
func (h *HealthCheckManager) saveStateToRedis() {
	if !h.useRedis {
		return
	}

	ctx := context.Background()
	key := h.redisKey + ":" + h.serverID

	healthyStr := "false"
	if h.isHealthy {
		healthyStr = "true"
	}

	fields := map[string]interface{}{
		"healthy":    healthyStr,
		"updated_at": time.Now().Format(time.RFC3339),
		"server_id":  h.serverID,
		"update_by":  "health_check_middleware",
	}

	redisManager.RedisManagerService.HMSet(ctx, key, fields)
}

// SetHealthy 设置健康状态
func (h *HealthCheckManager) SetHealthy(healthy bool) {
	h.mu.Lock()
	defer h.mu.Unlock()

	oldState := h.isHealthy
	h.isHealthy = healthy
	h.lastUpdate = time.Now()

	// 保存到Redis
	h.saveStateToRedis()

	log.AppLogger.Info("健康状态已更新",
		zap.String("server_id", h.serverID),
		zap.Bool("old_state", oldState),
		zap.Bool("new_state", h.isHealthy),
		zap.Time("updated_at", h.lastUpdate),
	)

	// 如果设置为不健康，启动优雅下线流程
	if !healthy && oldState {
		go h.startGracefulShutdown()
	}
}

// IsHealthy 获取当前健康状态
func (h *HealthCheckManager) IsHealthy() bool {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return h.isHealthy
}

// GetStatus 获取详细状态信息
func (h *HealthCheckManager) GetStatus() map[string]interface{} {
	h.mu.RLock()
	defer h.mu.RUnlock()

	status := map[string]interface{}{
		"healthy":     h.isHealthy,
		"server_id":   h.serverID,
		"last_update": h.lastUpdate.Format(time.RFC3339),
		"uptime":      time.Since(h.lastUpdate).String(),
		"use_redis":   h.useRedis,
	}

	// 如果使用Redis，获取Redis中的状态
	if h.useRedis {
		ctx := context.Background()
		key := h.redisKey + ":" + h.serverID
		redisStatus := redisManager.RedisManagerService.HGetAll(ctx, key)
		if len(redisStatus) > 0 {
			status["redis_status"] = redisStatus
		}
	}

	return status
}

// startGracefulShutdown 启动优雅下线流程
func (h *HealthCheckManager) startGracefulShutdown() {
	log.AppLogger.Info("启动优雅下线流程",
		zap.String("server_id", h.serverID),
		zap.Duration("grace_period", h.gracePeriod),
	)

	select {
	case <-time.After(h.gracePeriod):
		log.AppLogger.Info("优雅下线等待期结束", zap.String("server_id", h.serverID))
	case <-h.shutdownChan:
		log.AppLogger.Info("优雅下线被中断", zap.String("server_id", h.serverID))
	}
}

// CancelGracefulShutdown 取消优雅下线
func (h *HealthCheckManager) CancelGracefulShutdown() {
	select {
	case h.shutdownChan <- struct{}{}:
		log.AppLogger.Info("优雅下线已取消", zap.String("server_id", h.serverID))
	default:
		// 通道已满或没有等待的goroutine
	}
}

// HealthCheckMiddleware 健康检查中间件
func HealthCheckMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只处理健康检查相关的路径
		if c.Request.URL.Path == "/health" || c.Request.URL.Path == "/health/check" {
			manager := GetHealthCheckManager()

			if manager.IsHealthy() {
				app.SetSuccess(c, map[string]interface{}{
					"status":    "healthy",
					"server_id": manager.serverID,
					"timestamp": time.Now().Format(time.RFC3339),
				})
			} else {
				c.JSON(http.StatusServiceUnavailable, gin.H{
					"status":    "unhealthy",
					"server_id": manager.serverID,
					"timestamp": time.Now().Format(time.RFC3339),
					"message":   "Service is temporarily unavailable",
				})
			}
			c.Abort()
			return
		}

		c.Next()
	}
}

// HealthControlMiddleware 健康状态控制中间件
func HealthControlMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path

		switch path {
		case "/health/status":
			handleHealthStatus(c)
		case "/health/enable":
			handleHealthEnable(c)
		case "/health/disable":
			handleHealthDisable(c)
		//case "/health/toggle":
		//	handleHealthToggle(c)
		default:
			c.Next()
			return
		}

		c.Abort()
	}
}

// handleHealthStatus 处理健康状态查询
func handleHealthStatus(c *gin.Context) {
	manager := GetHealthCheckManager()
	status := manager.GetStatus()
	app.SetSuccess(c, status)
}

// handleHealthEnable 处理启用健康检查
func handleHealthEnable(c *gin.Context) {
	manager := GetHealthCheckManager()
	manager.SetHealthy(true)
	manager.CancelGracefulShutdown()

	app.SetSuccess(c, map[string]interface{}{
		"message":   "Health check enabled",
		"server_id": manager.serverID,
		"status":    "healthy",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleHealthDisable 处理禁用健康检查
func handleHealthDisable(c *gin.Context) {
	manager := GetHealthCheckManager()
	manager.SetHealthy(false)

	app.SetSuccess(c, map[string]interface{}{
		"message":   "Health check disabled",
		"server_id": manager.serverID,
		"status":    "unhealthy",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleHealthToggle 处理切换健康检查状态
func handleHealthToggle(c *gin.Context) {
	manager := GetHealthCheckManager()
	newState := !manager.IsHealthy()
	manager.SetHealthy(newState)

	if newState {
		manager.CancelGracefulShutdown()
	}

	status := "unhealthy"
	if newState {
		status = "healthy"
	}

	app.SetSuccess(c, map[string]interface{}{
		"message":   "Health check status toggled",
		"server_id": manager.serverID,
		"status":    status,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}
