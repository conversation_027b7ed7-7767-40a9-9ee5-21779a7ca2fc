package middleware

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/app"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/helper"
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
	"github.com/gin-gonic/gin"
)

var developSignature string = "wego&develop&just&test"

func SignCheck(ctx *gin.Context) {
	signM := utils.Signature{}
	var headers = make(map[string]any, 4)
	headers["X-SIGN"] = ctx.GetHeader("X-SIGN")
	// 开发用，越过签名校验
	if headers["X-SIGN"] == developSignature {
		ctx.Next()
		return
	}

	headers["X-TIME"] = ctx.GetHeader("x-TIME")
	headers["X-EXPIRED"] = ctx.GetHeader("X-EXPIRED")
	headers["X-APPKEY"] = ctx.GetHeader("X-APPKEY")
	headers["Content-Type"] = ctx.GetHeader("Content-Type")

	// 以post且传输格式为json，则json中的body也参与签名，如果未指定传输格式为json，则不参与签名
	bodyData := helper.GetJson(ctx)

	params := helper.GetQueryParams(ctx)

	err := signM.Check(headers, params, string(bodyData))
	if err != nil {
		app.SetError(ctx, err.Error(), 0)
		ctx.Abort()
		return
	}

	ctx.Next()
}
