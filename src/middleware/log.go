package middleware

import (
	"bytes"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/log"
	"go.uber.org/zap"
	"io"
	"time"

	"github.com/gin-gonic/gin"
)

func GinLogger() gin.HandlerFunc {
	return func(c *gin.Context) {

		start := time.Now()
		path := c.Request.URL.Path
		query := c.Request.URL.RawQuery

		b, err := c.GetRawData()

		requestBody := ""

		if err != nil {
			requestBody = "failed to get request body"
		} else {
			requestBody = string(b)

			//重置请求体
			c.Request.Body = io.NopCloser(bytes.NewBuffer(b))
		}

		writer := bodyWriter{
			c.Writer,
			bytes.NewBuffer([]byte{}),
		}

		c.Writer = writer

		c.Next()

		cost := time.Since(start)

		//响应状态码
		responseStatus := c.Writer.Status()

		responseBody := writer.body.String()

		log.AppLogger.Info(path,
			zap.Int("status", c.Writer.Status()),
			zap.String("method", c.Request.Method),
			zap.String("path", path),
			zap.String("query", query),
			zap.String("ip", c.ClientIP()),
			zap.String("user-agent", c.Request.UserAgent()),
			zap.String("requestBody", requestBody),
			zap.Int("responseStatus", responseStatus),
			zap.String("responseBody", responseBody),
			zap.Duration("cost", cost),
		)
	}
}

// 定义一个自定义的 ResponseWriter
type bodyWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// 重写 Write 方法，拦截响应数据
func (w bodyWriter) Write(b []byte) (int, error) {
	w.body.Write(b)                  // 将数据写入缓冲区
	return w.ResponseWriter.Write(b) // 调用原始的 Write 方法
}
