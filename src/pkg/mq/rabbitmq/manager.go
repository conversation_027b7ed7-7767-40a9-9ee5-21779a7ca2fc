package rabbitmq

import (
	"context"
	"fmt"
	"gitee.com/jianmengkeji/wego-common-pkg/src/consts"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/log"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/mq/base"
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
	"github.com/bytedance/sonic"
	"github.com/google/uuid"
	amqp "github.com/rabbitmq/amqp091-go"
	"go.uber.org/zap"
	"sync"
	"time"
)

type manager struct {
	config      *MQConfig
	queueMap    map[string]*queue
	producerMap map[string]*producer

	conn *amqp.Connection
}

func (manager *manager) GetProducer(producerName base.ProducerName) base.IProducer {
	return manager.producerMap[producerName.String()]
}

type Exchange struct {
	name       string
	tp         string
	durable    bool
	autoDelete bool
	internal   bool
	noWait     bool
	args       amqp.Table
}

type ExchangeBuilder struct {
	exchange *Exchange
}

func NewExchangeBuilder() *ExchangeBuilder {
	return &ExchangeBuilder{
		exchange: &Exchange{
			tp:         "direct",
			durable:    true,
			autoDelete: false,
			internal:   false,
			noWait:     false,
			args:       amqp.Table{},
		},
	}
}

func (b *ExchangeBuilder) WithName(name string) *ExchangeBuilder {
	b.exchange.name = name
	return b
}

func (b *ExchangeBuilder) WithType(tp string) *ExchangeBuilder {
	b.exchange.tp = tp
	return b
}

func (b *ExchangeBuilder) WithDurable(durable bool) *ExchangeBuilder {
	b.exchange.durable = durable
	return b
}

func (b *ExchangeBuilder) WithAutoDelete(autoDelete bool) *ExchangeBuilder {
	b.exchange.autoDelete = autoDelete
	return b
}

func (b *ExchangeBuilder) WithInternal(internal bool) *ExchangeBuilder {
	b.exchange.internal = internal
	return b
}

func (b *ExchangeBuilder) WithNoWait(noWait bool) *ExchangeBuilder {
	b.exchange.noWait = noWait
	return b
}

func (b *ExchangeBuilder) WithArgs(args amqp.Table) *ExchangeBuilder {
	b.exchange.args = args
	return b
}

func (b *ExchangeBuilder) Build() *Exchange {
	return b.exchange
}

type producer struct {
	name         string
	deliveryMode uint8
	autoConfirm  bool
	q            *queue
	ch           *amqp.Channel
	confirmsCh   chan *amqp.DeferredConfirmation
	returnedCh   chan amqp.Return
}

func (p *producer) GetName() string {
	return p.name
}

func (p *producer) Send(ctx context.Context, msg base.IMessage, options ...base.MsgOptions) error {
	for _, option := range options {
		option(msg)
	}
	pubConfig := amqp.Publishing{
		Body:         []byte(msg.GetText()),
		MessageId:    uuid.NewString(),
		DeliveryMode: p.deliveryMode,
	}
	rabbitMsg, ok := msg.(*message)
	if ok {
		pubConfig.ContentType = rabbitMsg.contentType
		pubConfig.Priority = rabbitMsg.priority
		pubConfig.Headers = rabbitMsg.headers
		pubConfig.Expiration = rabbitMsg.expiration
	}
	ch := p.ch
	q := p.q
	deferredConfirm, err := ch.PublishWithDeferredConfirmWithContext(ctx,
		q.exchange.name, // exchange
		q.name,          // routing key
		q.mandatory,     // mandatory
		q.immediate,     // immediate
		pubConfig)
	if err != nil {
		log.AppLogger.Error("send msg err, queue:%s, msg:%s, err:%v", zap.String("name", q.name),
			zap.String("msg", msg.GetText()),
			zap.Error(err))
		return err
	}
	if deferredConfirm != nil {
		p.confirmsCh <- deferredConfirm
	}
	return nil
}

type ProducerBuilder struct {
	producer *producer
}

func NewProducerBuilder() *ProducerBuilder {
	return &ProducerBuilder{
		producer: &producer{
			deliveryMode: amqp.Persistent,
		},
	}
}

func (b *ProducerBuilder) WithName(name base.ProducerName) *ProducerBuilder {
	b.producer.name = name.String()
	return b
}

func (b *ProducerBuilder) WithQueue(q base.IQueue) *ProducerBuilder {
	b.producer.q = q.(*queue)
	return b
}

func (b *ProducerBuilder) WithDeliveryMode(deliveryMode uint8) *ProducerBuilder {
	b.producer.deliveryMode = deliveryMode
	return b
}

func (b *ProducerBuilder) WithAutoConfirm(autoConfirm bool) *ProducerBuilder {
	b.producer.autoConfirm = autoConfirm
	return b
}

func (b *ProducerBuilder) Build() base.IProducer {
	return b.producer
}

type queue struct {
	name       string
	q          *amqp.Queue
	ch         *amqp.Channel
	exchange   *Exchange
	durable    bool
	autoDelete bool
	exclusive  bool
	noWait     bool
	mandatory  bool
	immediate  bool
	qos        int
	args       amqp.Table
	consumers  []*consumer
}

func (q *queue) GetName() string {
	return q.name
}

type ConsumerBuilder struct {
	consumer *consumer
}

func NewConsumerBuilder() *ConsumerBuilder {
	return &ConsumerBuilder{
		consumer: &consumer{
			args:         amqp.Table{},
			autoAck:      true,
			noLocal:      true,
			noWait:       false,
			exclusive:    false,
			requeueOnErr: false,
			num:          1,
			shareChannel: true,
		},
	}
}

func (b *ConsumerBuilder) WithName(name string) *ConsumerBuilder {
	b.consumer.name = name
	return b
}

func (b *ConsumerBuilder) WithRandomName() *ConsumerBuilder {
	b.consumer.name = uuid.NewString()
	return b
}

func (b *ConsumerBuilder) WithHandler(handler func(ctx context.Context, msg base.IMessage) error) *ConsumerBuilder {
	b.consumer.handler = handler
	return b
}

func (b *ConsumerBuilder) WithNum(num int) *ConsumerBuilder {
	b.consumer.num = num
	return b
}

func (b *ConsumerBuilder) WithAutoAck(autoAck bool) *ConsumerBuilder {
	b.consumer.autoAck = autoAck
	return b
}

func (b *ConsumerBuilder) WithExclusive(exclusive bool) *ConsumerBuilder {
	b.consumer.exclusive = exclusive
	return b
}

func (b *ConsumerBuilder) WithNoLocal(noLocal bool) *ConsumerBuilder {
	b.consumer.noLocal = noLocal
	return b
}

func (b *ConsumerBuilder) WithNoWait(noWait bool) *ConsumerBuilder {
	b.consumer.noWait = noWait
	return b
}

func (b *ConsumerBuilder) WithRequeueOnErr(requeueOnErr bool) *ConsumerBuilder {
	b.consumer.requeueOnErr = requeueOnErr
	return b
}

func (b *ConsumerBuilder) WithArgs(args amqp.Table) *ConsumerBuilder {
	b.consumer.args = args
	return b
}

func (b *ConsumerBuilder) WithShareChannel(shareChannel bool) *ConsumerBuilder {
	b.consumer.shareChannel = shareChannel
	return b
}

func (b *ConsumerBuilder) Build() base.IConsumer {
	return b.consumer
}

type consumer struct {
	ch           *amqp.Channel
	name         string
	handler      func(ctx context.Context, msg base.IMessage) error
	num          int
	autoAck      bool
	exclusive    bool
	noLocal      bool
	noWait       bool
	requeueOnErr bool
	shareChannel bool // 如果num>1，是否共享同一个channel
	args         amqp.Table
}

func (c *consumer) HandleMsg(ctx context.Context, msg base.IMessage) error {
	return nil
}

func (c *consumer) GetName() string {
	return c.name
}

type MessageBuilder struct {
	message *message
}

func NewMessageBuilder() *MessageBuilder {
	return &MessageBuilder{
		message: &message{
			contentType: "application/json",
			priority:    0,
		},
	}
}

func (b *MessageBuilder) WithText(text string) *MessageBuilder {
	b.message.text = text
	return b
}

func (b *MessageBuilder) WithJson(obj interface{}) *MessageBuilder {
	bytes, marshalErr := sonic.Marshal(obj)
	if marshalErr != nil {
		log.AppLogger.Error(fmt.Sprintf("marshal err:%v", marshalErr))
	}
	b.message.text = string(bytes)
	b.message.contentType = "application/json"
	return b
}

func (b *MessageBuilder) WithContentType(contentType string) *MessageBuilder {
	b.message.contentType = contentType
	return b
}

func (b *MessageBuilder) WithPriority(priority uint8) *MessageBuilder {
	b.message.priority = priority
	return b
}

func (b *MessageBuilder) WithExpiration(expiration string) *MessageBuilder {
	b.message.expiration = expiration
	return b
}

func (b *MessageBuilder) WithHeaders(headers amqp.Table) *MessageBuilder {
	b.message.headers = headers
	return b
}

func (b *MessageBuilder) Build() base.IMessage {
	return b.message
}

type message struct {
	text        string
	contentType string
	priority    uint8
	expiration  string
	headers     amqp.Table
}

func (m *message) GetText() string {
	return m.text
}

func (manager *manager) RegisterQueue(queueName base.QueueName, options ...base.QueueOptions) error {
	if manager.queueMap == nil {
		manager.queueMap = make(map[string]*queue)
	}
	q := &queue{name: queueName.String()}
	for _, option := range options {
		option(q)
	}
	manager.queueMap[queueName.String()] = q
	return nil
}

func (manager *manager) GetQueue(queueName base.QueueName) base.IQueue {
	return manager.queueMap[queueName.String()]
}

func (manager *manager) RegisterConsumer(queueName base.QueueName, consumers ...base.IConsumer) error {
	q := manager.queueMap[queueName.String()]
	if q == nil {
		return fmt.Errorf("queue %s not found", queueName)
	}
	for _, c := range consumers {
		if rabbitMQConsumer, ok := c.(*consumer); !ok {
			return fmt.Errorf("consumer %s is not rabbitmq consumer", c.GetName())
		} else {
			q.consumers = append(q.consumers, rabbitMQConsumer)
		}
	}
	return nil
}

func (manager *manager) RegisterProducer(queueName base.QueueName, producers ...base.IProducer) error {
	q := manager.queueMap[queueName.String()]
	if q == nil {
		return fmt.Errorf("queue %s not found", queueName)
	}
	if manager.producerMap == nil {
		manager.producerMap = make(map[string]*producer)
	}
	for _, p := range producers {
		if rabbitMQProducer, ok := p.(*producer); !ok {
			return fmt.Errorf("producer %s is not rabbitmq producer", p.GetName())
		} else {
			rabbitMQProducer.q = q
			rabbitMQProducer.confirmsCh = make(chan *amqp.DeferredConfirmation)
			rabbitMQProducer.returnedCh = make(chan amqp.Return)
			manager.producerMap[p.GetName()] = rabbitMQProducer
		}
	}
	return nil
}

func (manager *manager) Start(ctx context.Context) {
	uri := fmt.Sprintf("amqp://%s:%s@%s:%d/",
		manager.config.Username, manager.config.Password, manager.config.Host, manager.config.Port)
	config := amqp.Config{
		Vhost:      manager.config.Vhost,
		Properties: amqp.NewConnectionProperties(),
	}
	conn, dialErr := amqp.DialConfig(uri, config)
	failOnError(dialErr, "Failed to connect to RabbitMQ")
	manager.conn = conn

	for _, queue := range manager.queueMap {
		ch, channelErr := conn.Channel()
		failOnError(channelErr, "Failed to open a channel")
		q, err := ch.QueueDeclare(
			queue.GetName(),  // name
			queue.durable,    // durable
			queue.autoDelete, // delete when unused
			queue.exclusive,  // exclusive
			queue.noWait,     // no-wait
			queue.args,       // arguments
		)
		queue.q = &q
		queue.ch = ch
		failOnError(err, fmt.Sprintf("Failed to declare a queue: %s", queue.GetName()))

		if queue.qos > 0 {
			qosErr := ch.Qos(queue.qos, 0, false)
			failOnError(qosErr, fmt.Sprintf("Failed to set qos: %s", queue.GetName()))
		}

		ex := queue.exchange
		declareExchangeErr := ch.ExchangeDeclare(
			ex.name,
			ex.tp,
			ex.durable,
			ex.autoDelete,
			ex.internal,
			ex.noWait,
			ex.args)
		failOnError(declareExchangeErr, fmt.Sprintf("Failed to declare a exchange:%s", ex.name))

		bindErr := ch.QueueBind(
			queue.name,   // queue name
			queue.q.Name, // routing key
			ex.name,
			queue.noWait, nil)
		failOnError(bindErr, fmt.Sprintf("Failed to bind exchange to queue, queue name:%s, exchange name:%s", queue.q.Name, ex.name))
	}
	log.AppLogger.Info("RabbitMQ connected success!")

	// 启动消费者
	go func() {
		wg := sync.WaitGroup{}
		for _, q := range manager.queueMap {
			for _, c := range q.consumers {
				wg.Add(c.num)
				ch := q.ch
				if !c.shareChannel {
					var channelErr error
					ch, channelErr = conn.Channel()
					failOnError(channelErr, "Failed to open a channel")
				}
				c.ch = ch
				msgChannel, consumeErr := ch.Consume(
					q.q.Name,    // queue
					c.name,      // consumer tag
					c.autoAck,   // auto-ack
					c.exclusive, // exclusive
					c.noLocal,   // no-local
					c.noWait,    // no-wait
					c.args,      // args
				)
				failOnError(consumeErr, "Failed to consume msg")
				for i := 0; i < c.num; i++ {
					consumerIndex := i + 1
					go func(msgChan <-chan amqp.Delivery) {
						defer func() {
							if r := recover(); r != nil {
								log.AppLogger.Error(fmt.Sprintf("consumer panic, recovered. recover:%v, stack:%s", r, utils.Stack(3)))
							}
							wg.Done()
						}()
						consumerName := fmt.Sprintf("%s_consumer_%d", q.name, consumerIndex)
						for {
							select {
							case <-ctx.Done():
								log.AppLogger.Info(fmt.Sprintf("context done, consumer exit. consumer:%s", consumerName))
								return
							case ds := <-msgChan:
								msg := string(ds.Body)
								timeBegin := time.Now()
								ctx = context.WithValue(ctx, consts.TRACE_ID_KEY, uuid.New().String())
								log.AppLogger.Info(fmt.Sprintf("msg received, msg:%s", msg))
								handlerErr := c.handler(ctx, &message{text: msg})
								if handlerErr != nil {
									if !c.autoAck {
										ackErr := ds.Nack(false, c.requeueOnErr)
										if ackErr != nil {
											log.AppLogger.Error(fmt.Sprintf("ack err, consumer:%s, err:%v", consumerName, ackErr))
										}
									}
									log.AppLogger.Error(fmt.Sprintf("handler err, consumer:%s, err:%v, msg:%s", consumerName, handlerErr, msg))
								} else if !c.autoAck {
									ackErr := ds.Ack(false)
									if ackErr != nil {
										log.AppLogger.Error(fmt.Sprintf("ack err, consumer:%s, err:%v", consumerName, ackErr))
									}
								}
								log.AppLogger.Info(fmt.Sprintf("consume msg finish, consumer:%s, cost:%s", consumerName, time.Since(timeBegin)))
							default:
								time.Sleep(100 * time.Millisecond)
							}
						}
					}(msgChannel)
				}
			}
		}
		wg.Wait()
	}()

	// 启动生产者
	wg := sync.WaitGroup{}
	for _, p := range manager.producerMap {
		wg.Add(1)
		prd := p
		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.AppLogger.Error(fmt.Sprintf("producer panic, recovered. recover:%v, stack:%s", r))
				}
			}()
			if !prd.autoConfirm {
				channel, newChannelErr := manager.conn.Channel()
				if newChannelErr != nil {
					failOnError(newChannelErr, "Failed to open a channel")
				}
				if err := channel.Confirm(false); err != nil {
					failOnError(err, "Failed to enable publisher confirms.")
				}
				prd.ch = channel
			}
			prd.ch.NotifyReturn(prd.returnedCh)
			wg.Done()

			confirms := make(map[uint64]*amqp.DeferredConfirmation)
			for {
				select {
				case <-ctx.Done():
					log.AppLogger.Info(fmt.Sprintf("context done, producer exit. producer:%s", prd.GetName()))
					return
				case confirm := <-prd.confirmsCh:
					confirms[confirm.DeliveryTag] = confirm
					checkConfirmations(confirms)
					if len(confirms) > 100 {
						// 超过 100 条数据未被 ack， 先打日志
						// TODO 添加报警
						log.AppLogger.Error(fmt.Sprintf("confirm handler: waiting on %d outstanding confirmations, blocking publish, count:%d", len(confirms)))
					}
				case returned := <-prd.returnedCh:
					log.AppLogger.Error(fmt.Sprintf("msg returned, producer:%s, msg:%s, exchange:%s, routing key:%s",
						prd.GetName(), string(returned.Body), returned.Exchange, returned.RoutingKey))
				default:
					time.Sleep(100 * time.Millisecond)
				}
			}
		}()
	}
	wg.Wait()
}

func checkConfirmations(confirms map[uint64]*amqp.DeferredConfirmation) {
	for k, v := range confirms {
		if v.Acked() {
			delete(confirms, k)
		}
	}
}

func (manager *manager) Destroy(ctx context.Context) error {
	for _, q := range manager.queueMap {
		if !q.ch.IsClosed() {
			closeChannelErr := q.ch.Close()
			if closeChannelErr != nil {
				log.AppLogger.Warn(fmt.Sprintf("close channel error:%v", closeChannelErr))
				continue
			}
		}
	}
	closeConnectionErr := manager.conn.Close()
	if closeConnectionErr != nil {
		log.AppLogger.Warn(fmt.Sprintf("close connection error:%v", closeConnectionErr))
		return closeConnectionErr
	}
	return nil
}

func failOnError(err error, msg string) {
	if err != nil {
		log.AppLogger.Error(msg, zap.Error(err))
	}
}

type MQConfig struct {
	Host     string
	Port     int
	Username string
	Password string
	Vhost    string
}

func NewRabbitMQManager(config *MQConfig) base.IManager {
	return &manager{config: config}
}

var DefaultQueueOption = func(queueName base.QueueName) func(q base.IQueue) {
	return func(q base.IQueue) {
		qe := q.(*queue)
		qe.durable = true
		qe.autoDelete = false
		qe.exclusive = false
		qe.noWait = false
		qe.mandatory = true
		qe.immediate = false
		qe.args = nil
		qe.name = queueName.String()
		qe.qos = 100
		qe.exchange = NewExchangeBuilder().WithName(queueName.String()).Build()
	}
}

var WithExchangeOption = func(queueName base.QueueName, exchange *Exchange) func(q base.IQueue) {
	return func(q base.IQueue) {
		qe := q.(*queue)
		qe.durable = true
		qe.autoDelete = false
		qe.exclusive = false
		qe.noWait = false
		qe.mandatory = true
		qe.immediate = false
		qe.args = nil
		qe.name = queueName.String()
		qe.qos = 100
		qe.exchange = exchange
		qe.args = amqp.Table{
			"x-queue-type": "quorum",
		}
	}
}

var WithQosOption = func(qos int) func(q base.IQueue) {
	return func(q base.IQueue) {
		q.(*queue).qos = qos
	}
}
