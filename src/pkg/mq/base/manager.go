package base

import (
	"context"
)

type IManager interface {
	RegisterQueue(queueName QueueName, options ...QueueOptions) error
	GetQueue(queueName QueueName) IQueue
	RegisterConsumer(queueName QueueName, consumers ...IConsumer) error
	RegisterProducer(queueName QueueName, producers ...IProducer) error
	GetProducer(producerName ProducerName) IProducer
	Start(ctx context.Context)
	Destroy(ctx context.Context) error
}

type (
	QueueName    string
	ProducerName string
)

func (q QueueName) String() string {
	return string(q)
}

func (p ProducerName) String() string {
	return string(p)
}

type IMessage interface {
	GetText() string
}

type IConsumer interface {
	GetName() string
	HandleMsg(ctx context.Context, msg IMessage) error
}

type IProducer interface {
	GetName() string
	Send(ctx context.Context, msg IMessage, option ...MsgOptions) error
}

type (
	QueueOptions func(queue IQueue)
	MsgOptions   func(msg IMessage)
	ConsumerOpts func(consumer IConsumer)
)

type IQueue interface {
	GetName() string
}
