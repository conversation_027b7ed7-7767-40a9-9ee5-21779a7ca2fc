package log

// log
type Config struct {
	Mode         string `yaml:"mode"`
	EnableFile   bool   `yaml:"enable_file_log_writer"`
	RootDir      string `yaml:"root_dir"`
	Filename     string `yaml:"filename"`
	MaxSize      int    `yaml:"max_size"`
	MaxBackups   int    `yaml:"max_backups"`
	MaxAge       int    `yaml:"max_age"`
	Compress     bool   `yaml:"compress"`
	UseLocalTime bool   `yaml:"use_local_time"`
}
