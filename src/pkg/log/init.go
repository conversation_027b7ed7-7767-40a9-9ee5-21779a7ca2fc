package log

import (
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"log"
	"os"
	"path/filepath"
)

var AppLogger *zap.Logger

func InitLog(config *Config) {
	log.Println("日志组件初始化...")
	createRootDir(config.RootDir)
	// 配置 lumberjack 日志轮转
	lumberjackLogger := &lumberjack.Logger{
		Filename:   filepath.Join(config.RootDir, config.Filename),
		MaxSize:    config.MaxSize, // megabytes
		MaxBackups: config.MaxBackups,
		MaxAge:     config.MaxAge,   // days
		Compress:   config.Compress, // 是否压缩/归档旧文件
		LocalTime:  config.UseLocalTime,
	}

	// 配置 zap 编码器
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.StringDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(encoderConfig), // 使用 JSON 编码器
		zapcore.AddSync(lumberjackLogger),     // 使用 lumberjack 进行日志轮转
		zap.InfoLevel,                         // 日志级别
	)

	// 创建日志记录器
	AppLogger = zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	AppLogger.Sync()

	log.Println("日志组件初始化完成")
}

func createRootDir(RootUrl string) {
	if ok, _ := PathExists(RootUrl); !ok {
		_ = os.Mkdir(RootUrl, os.ModePerm)
	}
}

func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}
