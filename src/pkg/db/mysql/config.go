package mysql

import "time"

type MysqlDbConfig struct {
	Db1dsn          string        `yaml:"db1_dsn"`
	Db2dsn          string        `yaml:"db2_dsn"`
	Db3dsn          string        `yaml:"db3_dsn"`
	Prefix          string        `yaml:"prefix" default:""`
	SingularTable   bool          `yaml:"singularTable" default:"false"`
	MaxIdleConns    int           `yaml:"maxIdleConns" default:"10"`
	MaxOpenConns    int           `yaml:"maxOpenConns" default:"10"`
	ConnMaxLifetime time.Duration `yaml:"connMaxLifetime" default:"3600"`
}
