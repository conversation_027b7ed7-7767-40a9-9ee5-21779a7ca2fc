package mysql

import (
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
	"gorm.io/plugin/dbresolver"
	"log"
)

var MysqlDb *gorm.DB

func dbConnect(Config *MysqlDbConfig) *gorm.DB {
	db1Dsn := Config.Db1dsn
	db2Dsn := Config.Db2dsn
	db3Dsn := Config.Db3dsn
	db, err := gorm.Open(mysql.Open(db1Dsn), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   Config.Prefix,
			SingularTable: Config.SingularTable,
		},
	})

	db.Use(dbresolver.Register(dbresolver.Config{
		// `db1` 作为 sources，`db2`、`db3` 作为 replicas
		Sources:  []gorm.Dialector{mysql.Open(db1Dsn)},
		Replicas: []gorm.Dialector{mysql.Open(db2Dsn), mysql.Open(db3Dsn)},
		// sources/replicas 负载均衡策略
		Policy: dbresolver.RandomPolicy{},
	}))

	//使用mysql 连接池
	sqlDB, _ := db.DB()

	// SetMaxIdleConns 用于设置连接池中空闲连接的最大数量。
	sqlDB.SetMaxIdleConns(Config.MaxIdleConns)

	// SetMaxOpenConns 设置打开数据库连接的最大数量。
	sqlDB.SetMaxOpenConns(Config.MaxOpenConns)

	// SetConnMaxLifetime 设置了连接可复用的最大时间。
	sqlDB.SetConnMaxLifetime(Config.ConnMaxLifetime)

	//sqlDB.SetConnMaxIdleTime(time.Minute * 2)

	if err != nil {
		panic(err)
	}
	return db
}

func InitMysqlDb(Config *MysqlDbConfig) {
	log.Println("初始化DB中...")
	MysqlDb = dbConnect(Config)
	log.Println("DB初始化完成！")
}
