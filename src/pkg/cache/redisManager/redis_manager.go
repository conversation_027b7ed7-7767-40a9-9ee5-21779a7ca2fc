package redisManager

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/helper"
	"github.com/go-redis/redis/v8"
	"github.com/syyongx/php2go"
	"strconv"
	"time"
)

var tag = "tag_"

type RedisInterface interface {
	GetCacheKey(key string) string
	Get(ctx context.Context, key string) (interface{}, bool)
	Set(ctx context.Context, key string, value interface{}, expired int64)
	Rmember(ctx context.Context, cacheKey string, callQuery func() string, expired int64) string
	Lpush(ctx context.Context, key string, value interface{}) int64
	Rpush(ctx context.Context, key string, value interface{}) int64
	ZADD(ctx context.Context, key string, member string, score float64)
	ZCARD(ctx context.Context, key string) bool
	ZRANK(ctx context.Context, key string, member string) int64
	ZINCRBY(ctx context.Context, key string, increment float64, member string)
	ZREVRANGE(ctx context.Context, key string, start int64, end int64) []string
	Lpop(ctx context.Context, key string) string
	Rpop(ctx context.Context, key string) string
	LpopMul(ctx context.Context, key string, len int64) []string
	Llen(ctx context.Context, key string) int64
	Del(ctx context.Context, key string)
	Tag(ctx context.Context, tagKey string, keySlice []string)
	TagClear(ctx context.Context, tagKey string)
	MSet(ctx context.Context, key string, values map[string]interface{}) string
	SetMulSplit(ctx context.Context, splitSize int, value string, key string)
	MgetSplit(ctx context.Context, cacheKey string, splitSize int) interface{}
	RmemberSplit(ctx context.Context, cacheKey string, callQuery func() interface{}, splitSize int) interface{}
}

var RedisManagerService RedisInterface = &redisManagerService{}

type redisManagerService struct {
}

// GetCacheKey 获取缓存key值
func (r *redisManagerService) GetCacheKey(key string) string {
	prefix := RedisPrefix
	return prefix + key
}

// Get 获取指定key值  如果redis没有这个key 返回"",false
func (r *redisManagerService) Get(ctx context.Context, key string) (interface{}, bool) {
	key = r.GetCacheKey(key)
	res, err := Redis.Get(ctx, key).Result()

	if errors.Is(err, redis.Nil) {
		return "", false
	} else if err != nil {
		panic(err)
	} else {
		var data interface{}

		err := json.Unmarshal([]byte(res), &data)

		if err != nil {
			panic(err)
		}

		return data, true
	}
}

// Set 设置指定key值
func (r *redisManagerService) Set(ctx context.Context, key string, value interface{}, expired int64) {

	key = r.GetCacheKey(key)

	dataType, _ := json.Marshal(value)

	dataString := string(dataType)

	_, err := Redis.Set(ctx, key, dataString, time.Duration(expired)*time.Second).Result()

	if err != nil {
		panic(err)
	}
}

// redis 操作已经简化
func (r *redisManagerService) Rmember(ctx context.Context, cacheKey string, callQuery func() string, expired int64) string {

	// 此处通过 redis 获取数据, 如果存在数据, 那么直接返回
	data, err := r.Get(ctx, cacheKey)

	if err != false {
		return data.(string)
	}

	// 当 redis 没有数据, 那么调用此方法修改 t,
	saveData := callQuery()

	r.Set(ctx, cacheKey, saveData, expired)

	return saveData
}

// 推队列 左推 返回当前队列数量
func (r *redisManagerService) Lpush(ctx context.Context, key string, value interface{}) int64 {
	key = r.GetCacheKey(key)

	dataType, _ := json.Marshal(value)

	dataString := string(dataType)

	res, err := Redis.LPush(ctx, key, dataString).Result()

	if err != nil {
		panic(err)
	}

	return res
}

// 右推
func (r *redisManagerService) Rpush(ctx context.Context, key string, value interface{}) int64 {

	key = r.GetCacheKey(key)

	dataType, _ := json.Marshal(value)

	dataString := string(dataType)

	res, err := Redis.RPush(ctx, key, dataString).Result()

	if err != nil {
		panic(err)
	}

	return res
}

// 右推
func (r *redisManagerService) ZADD(ctx context.Context, key string, member string, score float64) {

	key = r.GetCacheKey(key)

	items := &redis.Z{Score: score, Member: member}

	_, err := Redis.ZAdd(ctx, key, items).Result()

	if err != nil {
		panic(err)
	}

}

// 获取有序集合的成员数
func (r *redisManagerService) ZCARD(ctx context.Context, key string) bool {

	key = r.GetCacheKey(key)

	res, err := Redis.ZCard(ctx, key).Result()

	if err != nil {
		panic(err)
	}

	if res > 0 {
		return true
	} else {
		return false
	}

}

// 返回有序集合中指定成员的索引
func (r *redisManagerService) ZRANK(ctx context.Context, key string, member string) int64 {

	key = r.GetCacheKey(key)

	res, err := Redis.ZRank(ctx, key, member).Result()

	if err != nil {
		if err == redis.Nil {
			return -1
		}
	}

	return res

}

// 获取有序集合的成员数
func (r *redisManagerService) ZINCRBY(ctx context.Context, key string, incryment float64, member string) {

	key = r.GetCacheKey(key)

	_, err := Redis.ZIncrBy(ctx, key, incryment, member).Result()

	if err != nil {
		panic(err)
	}
}

func (r *redisManagerService) ZREVRANGE(ctx context.Context, key string, start int64, end int64) []string {

	key = r.GetCacheKey(key)

	res, err := Redis.ZRevRange(ctx, key, start, end).Result()

	if err != nil {
		panic(err)
	}

	return res
}

// 弹队列 左推 返回当前队列数量
func (r *redisManagerService) Lpop(ctx context.Context, key string) string {

	key = r.GetCacheKey(key)

	res, err := Redis.LPop(ctx, key).Result()

	if err != nil {
		panic(err)
	}

	return res
}

// 弹队列 右弹 返回当前队列数量
func (r *redisManagerService) Rpop(ctx context.Context, key string) string {
	key = r.GetCacheKey(key)

	res, err := Redis.RPop(ctx, key).Result()

	if err != nil {
		panic(err)
	}

	return res
}

// 弹队列 左弹多个值 返回当前队列数量
func (r *redisManagerService) LpopMul(ctx context.Context, key string, len int64) []string {

	key = r.GetCacheKey(key)

	res, err := Redis.LRange(ctx, key, 0, len-1).Result()
	Redis.LTrim(ctx, key, len, -1).Result()

	if err != nil {
		panic(err)
	}

	return res
}

// 弹队列 右弹 返回当前队列数量
func (r *redisManagerService) Llen(ctx context.Context, key string) int64 {

	key = r.GetCacheKey(key)

	res, err := Redis.LLen(ctx, key).Result()

	if err != nil {
		panic(err)
	}

	return res
}

// 删除缓存
func (r *redisManagerService) Del(ctx context.Context, key string) {

	key = r.GetCacheKey(key)

	_, err := Redis.Del(ctx, key).Result()

	if err != nil {
		panic(err)
	}
}

// 给缓存打标签
func (r *redisManagerService) Tag(ctx context.Context, tagKey string, keySlice []string) {
	//首先获取当前tagKey下面的缓存
	tagKeyEncryption := tag + php2go.Md5(tagKey)

	tagkeyRes, res := r.Get(ctx, tagKeyEncryption)

	keyStr := php2go.Implode(",", keySlice)
	//没有tagkey
	if res == false {

		r.Set(ctx, tagKeyEncryption, keyStr, 0)

	} else {

		tagKeySlice := php2go.Explode(",", tagkeyRes.(string))

		for _, value := range keySlice {
			if php2go.InArray(value, tagKeySlice) {
				continue
			}
			tagKeySlice = append(tagKeySlice, value)
		}

		newKeyStr := php2go.Implode(",", tagKeySlice)

		r.Set(ctx, tagKeyEncryption, newKeyStr, 0)

	}

}

// 给缓存打标签
func (r *redisManagerService) TagClear(ctx context.Context, tagKey string) {
	//首先获取当前tagKey下面的缓存
	tagKeyEncryption := tag + php2go.Md5(tagKey)

	fmt.Println(tagKeyEncryption)

	tagkeyRes, res := r.Get(ctx, tagKeyEncryption)

	fmt.Println(tagkeyRes)

	if res == true {
		tagKeySlice := php2go.Explode(",", tagkeyRes.(string))

		for _, value := range tagKeySlice {
			r.Del(ctx, value)
		}

		r.Del(ctx, tagKeyEncryption)

	}
}

// 批量设置Mset
func (r *redisManagerService) MSet(ctx context.Context, key string, values map[string]interface{}) string {
	key = r.GetCacheKey(key)

	res, err := Redis.MSet(ctx, values).Result()

	if err != nil {
		panic(err)
	}

	return res
}

func (r *redisManagerService) SetMulSplit(ctx context.Context, splitSize int, value string, key string) {
	var needSaveData []string
	res := helper.SplitValue(value, splitSize)
	for i, item := range res {
		tmpKey := key + ":" + strconv.Itoa(i)
		needSaveData = append(needSaveData, tmpKey)
		needSaveData = append(needSaveData, item)
	}

	key = r.GetCacheKey(key)

	err := Redis.MSet(ctx, needSaveData).Err()
	if err != nil {
		panic(err)
	}
}

func (r *redisManagerService) MgetSplit(ctx context.Context, cacheKey string, splitSize int) interface{} {
	var keysSlice []string
	for i := 0; i < splitSize; i++ {
		key := r.GetCacheKey(cacheKey) + ":" + strconv.Itoa(i)
		keysSlice = append(keysSlice, key)
	}

	data, err := Redis.MGet(ctx, keysSlice...).Result()
	if err != nil {
		panic(err)
	}
	return data
}

// redis 操作已经简化
func (r *redisManagerService) RmemberSplit(ctx context.Context, cacheKey string, callQuery func() interface{}, splitSize int) interface{} {

	// 此处通过 redis 获取数据, 如果存在数据, 那么直接返回
	data := r.MgetSplit(ctx, cacheKey, splitSize)

	if php2go.Empty(data) {
		return data
	}

	// 当 redis 没有数据, 那么调用此方法修改 t,
	saveData := callQuery()

	encode, error := json.Marshal(saveData)

	if error != nil {
		panic(error)
	}

	r.SetMulSplit(ctx, splitSize, string(encode), cacheKey)

	return saveData
}
