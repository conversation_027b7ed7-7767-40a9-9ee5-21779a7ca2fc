package redisManager

import (
	"context"
	"github.com/go-redis/redis/v8"
	"log"
)

var Redis *redis.Client
var RedisPrefix string

// 初始化redis连接
func Connect(config *RedisConfig) *redis.Client {
	name := config.Host
	port := config.Port
	password := config.Password
	db := config.Db
	r := redis.NewClient(&redis.Options{
		Addr:     name + ":" + port,
		Password: password, // no password set
		DB:       db,       // use default DB

		PoolSize: 10,
	})

	_, err := r.Ping(context.Background()).Result()

	if err != nil {
		panic("redis 连接失败!")
	}

	return r
}

// 初始化Redis
func InitRedis(config *RedisConfig) {
	log.Println("初始化Redis中...")
	Redis = Connect(config)
	RedisPrefix = config.Prefix
	log.Println("Redis初始化完成！")
}
