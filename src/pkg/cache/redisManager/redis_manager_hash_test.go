package redisManager

import (
	"context"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"testing"
)

// RedisHashTestSuite Redis Hash操作测试套件
type RedisHashTestSuite struct {
	suite.Suite
	ctx     context.Context
	testKey string
}

// SetupSuite 测试套件初始化
func (suite *RedisHashTestSuite) SetupSuite() {
	suite.ctx = context.Background()
	suite.testKey = "test_hash"

	// 初始化Redis连接（测试环境）
	config := &RedisConfig{
		Host:     "localhost",
		Port:     "6379",
		Password: "",
		Db:       1, // 使用测试数据库
		Prefix:   "test_",
	}

	InitRedis(config)
}

// SetupTest 每个测试前的准备工作
func (suite *RedisHashTestSuite) SetupTest() {
	// 清理测试数据
	RedisManagerService.Del(suite.ctx, suite.testKey)
}

// TearDownTest 每个测试后的清理工作
func (suite *RedisHashTestSuite) TearDownTest() {
	// 清理测试数据
	RedisManagerService.Del(suite.ctx, suite.testKey)
}

// TestHSet 测试HSet操作
func (suite *RedisHashTestSuite) TestHSet() {
	// 测试设置字符串值
	result := RedisManagerService.HSet(suite.ctx, suite.testKey, "name", "张三")
	assert.True(suite.T(), result, "HSet应该返回true表示新字段被创建")

	// 测试设置数字值
	result = RedisManagerService.HSet(suite.ctx, suite.testKey, "age", 25)
	assert.True(suite.T(), result, "HSet应该返回true表示新字段被创建")

	// 测试设置复杂对象
	user := map[string]interface{}{
		"id":   1,
		"name": "李四",
		"tags": []string{"golang", "redis"},
	}
	result = RedisManagerService.HSet(suite.ctx, suite.testKey, "user", user)
	assert.True(suite.T(), result, "HSet应该能够设置复杂对象")

	// 测试更新已存在的字段
	result = RedisManagerService.HSet(suite.ctx, suite.testKey, "name", "王五")
	assert.False(suite.T(), result, "HSet更新已存在字段应该返回false")
}

// TestHGet 测试HGet操作
func (suite *RedisHashTestSuite) TestHGet() {
	// 先设置一些测试数据
	RedisManagerService.HSet(suite.ctx, suite.testKey, "name", "张三")
	RedisManagerService.HSet(suite.ctx, suite.testKey, "age", 25)

	// 测试获取存在的字段
	value, exists := RedisManagerService.HGet(suite.ctx, suite.testKey, "name")
	assert.True(suite.T(), exists, "字段应该存在")
	assert.Equal(suite.T(), "张三", value, "应该返回正确的值")

	value, exists = RedisManagerService.HGet(suite.ctx, suite.testKey, "age")
	assert.True(suite.T(), exists, "字段应该存在")
	assert.Equal(suite.T(), float64(25), value, "应该返回正确的数字值")

	// 测试获取不存在的字段
	value, exists = RedisManagerService.HGet(suite.ctx, suite.testKey, "nonexistent")
	assert.False(suite.T(), exists, "不存在的字段应该返回false")
	assert.Equal(suite.T(), "", value, "不存在的字段应该返回空字符串")
}

// TestHGetAll 测试HGetAll操作
func (suite *RedisHashTestSuite) TestHGetAll() {
	// 先设置一些测试数据
	RedisManagerService.HSet(suite.ctx, suite.testKey, "name", "张三")
	RedisManagerService.HSet(suite.ctx, suite.testKey, "age", 25)
	RedisManagerService.HSet(suite.ctx, suite.testKey, "city", "北京")

	// 获取所有字段
	result := RedisManagerService.HGetAll(suite.ctx, suite.testKey)

	assert.Len(suite.T(), result, 3, "应该返回3个字段")
	assert.Equal(suite.T(), "张三", result["name"], "name字段值应该正确")
	assert.Equal(suite.T(), float64(25), result["age"], "age字段值应该正确")
	assert.Equal(suite.T(), "北京", result["city"], "city字段值应该正确")

	// 测试空哈希表
	emptyResult := RedisManagerService.HGetAll(suite.ctx, "nonexistent_hash")
	assert.Empty(suite.T(), emptyResult, "不存在的哈希表应该返回空map")
}

// TestHDel 测试HDel操作
func (suite *RedisHashTestSuite) TestHDel() {
	// 先设置一些测试数据
	RedisManagerService.HSet(suite.ctx, suite.testKey, "name", "张三")
	RedisManagerService.HSet(suite.ctx, suite.testKey, "age", 25)
	RedisManagerService.HSet(suite.ctx, suite.testKey, "city", "北京")

	// 删除单个字段
	deletedCount := RedisManagerService.HDel(suite.ctx, suite.testKey, "age")
	assert.Equal(suite.T(), int64(1), deletedCount, "应该删除1个字段")

	// 验证字段已被删除
	_, exists := RedisManagerService.HGet(suite.ctx, suite.testKey, "age")
	assert.False(suite.T(), exists, "被删除的字段应该不存在")

	// 删除多个字段
	deletedCount = RedisManagerService.HDel(suite.ctx, suite.testKey, "name", "city")
	assert.Equal(suite.T(), int64(2), deletedCount, "应该删除2个字段")

	// 删除不存在的字段
	deletedCount = RedisManagerService.HDel(suite.ctx, suite.testKey, "nonexistent")
	assert.Equal(suite.T(), int64(0), deletedCount, "删除不存在的字段应该返回0")
}

// TestHExists 测试HExists操作
func (suite *RedisHashTestSuite) TestHExists() {
	// 先设置一些测试数据
	RedisManagerService.HSet(suite.ctx, suite.testKey, "name", "张三")

	// 测试存在的字段
	exists := RedisManagerService.HExists(suite.ctx, suite.testKey, "name")
	assert.True(suite.T(), exists, "存在的字段应该返回true")

	// 测试不存在的字段
	exists = RedisManagerService.HExists(suite.ctx, suite.testKey, "nonexistent")
	assert.False(suite.T(), exists, "不存在的字段应该返回false")

	// 测试不存在的哈希表
	exists = RedisManagerService.HExists(suite.ctx, "nonexistent_hash", "field")
	assert.False(suite.T(), exists, "不存在的哈希表应该返回false")
}

// TestHKeys 测试HKeys操作
func (suite *RedisHashTestSuite) TestHKeys() {
	// 先设置一些测试数据
	RedisManagerService.HSet(suite.ctx, suite.testKey, "name", "张三")
	RedisManagerService.HSet(suite.ctx, suite.testKey, "age", 25)
	RedisManagerService.HSet(suite.ctx, suite.testKey, "city", "北京")

	// 获取所有字段名
	keys := RedisManagerService.HKeys(suite.ctx, suite.testKey)

	assert.Len(suite.T(), keys, 3, "应该返回3个字段名")
	assert.Contains(suite.T(), keys, "name", "应该包含name字段")
	assert.Contains(suite.T(), keys, "age", "应该包含age字段")
	assert.Contains(suite.T(), keys, "city", "应该包含city字段")

	// 测试空哈希表
	emptyKeys := RedisManagerService.HKeys(suite.ctx, "nonexistent_hash")
	assert.Empty(suite.T(), emptyKeys, "不存在的哈希表应该返回空切片")
}

// TestHVals 测试HVals操作
func (suite *RedisHashTestSuite) TestHVals() {
	// 先设置一些测试数据
	RedisManagerService.HSet(suite.ctx, suite.testKey, "name", "张三")
	RedisManagerService.HSet(suite.ctx, suite.testKey, "age", 25)
	RedisManagerService.HSet(suite.ctx, suite.testKey, "city", "北京")

	// 获取所有值
	values := RedisManagerService.HVals(suite.ctx, suite.testKey)

	assert.Len(suite.T(), values, 3, "应该返回3个值")
	assert.Contains(suite.T(), values, "张三", "应该包含name的值")
	assert.Contains(suite.T(), values, float64(25), "应该包含age的值")
	assert.Contains(suite.T(), values, "北京", "应该包含city的值")

	// 测试空哈希表
	emptyValues := RedisManagerService.HVals(suite.ctx, "nonexistent_hash")
	assert.Empty(suite.T(), emptyValues, "不存在的哈希表应该返回空切片")
}

// TestHLen 测试HLen操作
func (suite *RedisHashTestSuite) TestHLen() {
	// 测试空哈希表
	length := RedisManagerService.HLen(suite.ctx, suite.testKey)
	assert.Equal(suite.T(), int64(0), length, "空哈希表长度应该为0")

	// 添加字段并测试长度
	RedisManagerService.HSet(suite.ctx, suite.testKey, "name", "张三")
	length = RedisManagerService.HLen(suite.ctx, suite.testKey)
	assert.Equal(suite.T(), int64(1), length, "添加1个字段后长度应该为1")

	RedisManagerService.HSet(suite.ctx, suite.testKey, "age", 25)
	length = RedisManagerService.HLen(suite.ctx, suite.testKey)
	assert.Equal(suite.T(), int64(2), length, "添加2个字段后长度应该为2")

	// 删除字段并测试长度
	RedisManagerService.HDel(suite.ctx, suite.testKey, "name")
	length = RedisManagerService.HLen(suite.ctx, suite.testKey)
	assert.Equal(suite.T(), int64(1), length, "删除1个字段后长度应该为1")
}

// TestHIncrBy 测试HIncrBy操作
func (suite *RedisHashTestSuite) TestHIncrBy() {
	// 测试对不存在的字段进行增量操作
	result := RedisManagerService.HIncrBy(suite.ctx, suite.testKey, "counter", 5)
	assert.Equal(suite.T(), int64(5), result, "对不存在字段的增量操作应该返回增量值")

	// 测试对已存在字段进行增量操作
	result = RedisManagerService.HIncrBy(suite.ctx, suite.testKey, "counter", 3)
	assert.Equal(suite.T(), int64(8), result, "对已存在字段的增量操作应该返回累加值")

	// 测试负数增量
	result = RedisManagerService.HIncrBy(suite.ctx, suite.testKey, "counter", -2)
	assert.Equal(suite.T(), int64(6), result, "负数增量应该正确计算")

	// 验证最终值
	value, exists := RedisManagerService.HGet(suite.ctx, suite.testKey, "counter")
	assert.True(suite.T(), exists, "字段应该存在")
	assert.Equal(suite.T(), float64(6), value, "最终值应该正确")
}

// TestHIncrByFloat 测试HIncrByFloat操作
func (suite *RedisHashTestSuite) TestHIncrByFloat() {
	// 测试对不存在的字段进行浮点增量操作
	result := RedisManagerService.HIncrByFloat(suite.ctx, suite.testKey, "score", 3.14)
	assert.Equal(suite.T(), 3.14, result, "对不存在字段的浮点增量操作应该返回增量值")

	// 测试对已存在字段进行浮点增量操作
	result = RedisManagerService.HIncrByFloat(suite.ctx, suite.testKey, "score", 2.86)
	assert.InDelta(suite.T(), 6.0, result, 0.001, "对已存在字段的浮点增量操作应该返回累加值")

	// 测试负数浮点增量
	result = RedisManagerService.HIncrByFloat(suite.ctx, suite.testKey, "score", -1.5)
	assert.InDelta(suite.T(), 4.5, result, 0.001, "负数浮点增量应该正确计算")
}

// TestHMSet 测试HMSet操作
func (suite *RedisHashTestSuite) TestHMSet() {
	// 测试批量设置字段
	fields := map[string]interface{}{
		"name":    "张三",
		"age":     25,
		"city":    "北京",
		"salary":  8000.50,
		"married": true,
	}

	result := RedisManagerService.HMSet(suite.ctx, suite.testKey, fields)
	assert.True(suite.T(), result, "HMSet应该返回true")

	// 验证所有字段都被正确设置
	allFields := RedisManagerService.HGetAll(suite.ctx, suite.testKey)
	assert.Len(suite.T(), allFields, 5, "应该设置5个字段")
	assert.Equal(suite.T(), "张三", allFields["name"])
	assert.Equal(suite.T(), float64(25), allFields["age"])
	assert.Equal(suite.T(), "北京", allFields["city"])
	assert.Equal(suite.T(), 8000.50, allFields["salary"])
	assert.Equal(suite.T(), true, allFields["married"])

	// 测试更新已存在的字段
	updateFields := map[string]interface{}{
		"age":  26,
		"city": "上海",
	}

	result = RedisManagerService.HMSet(suite.ctx, suite.testKey, updateFields)
	assert.True(suite.T(), result, "HMSet更新字段应该返回true")

	// 验证字段被正确更新
	value, _ := RedisManagerService.HGet(suite.ctx, suite.testKey, "age")
	assert.Equal(suite.T(), float64(26), value, "age字段应该被更新")

	value, _ = RedisManagerService.HGet(suite.ctx, suite.testKey, "city")
	assert.Equal(suite.T(), "上海", value, "city字段应该被更新")
}

// TestHMGet 测试HMGet操作
func (suite *RedisHashTestSuite) TestHMGet() {
	// 先设置一些测试数据
	fields := map[string]interface{}{
		"name": "张三",
		"age":  25,
		"city": "北京",
	}
	RedisManagerService.HMSet(suite.ctx, suite.testKey, fields)

	// 测试获取多个存在的字段
	values := RedisManagerService.HMGet(suite.ctx, suite.testKey, "name", "age", "city")
	assert.Len(suite.T(), values, 3, "应该返回3个值")
	assert.Equal(suite.T(), "张三", values[0], "第一个值应该是name")
	assert.Equal(suite.T(), float64(25), values[1], "第二个值应该是age")
	assert.Equal(suite.T(), "北京", values[2], "第三个值应该是city")

	// 测试获取部分存在的字段
	values = RedisManagerService.HMGet(suite.ctx, suite.testKey, "name", "nonexistent", "age")
	assert.Len(suite.T(), values, 3, "应该返回3个值")
	assert.Equal(suite.T(), "张三", values[0], "第一个值应该是name")
	assert.Nil(suite.T(), values[1], "不存在的字段应该返回nil")
	assert.Equal(suite.T(), float64(25), values[2], "第三个值应该是age")

	// 测试获取不存在的哈希表的字段
	values = RedisManagerService.HMGet(suite.ctx, "nonexistent_hash", "field1", "field2")
	assert.Len(suite.T(), values, 2, "应该返回2个值")
	assert.Nil(suite.T(), values[0], "不存在哈希表的字段应该返回nil")
	assert.Nil(suite.T(), values[1], "不存在哈希表的字段应该返回nil")
}

// TestComplexDataTypes 测试复杂数据类型
func (suite *RedisHashTestSuite) TestComplexDataTypes() {
	// 测试存储和获取复杂对象
	user := map[string]interface{}{
		"id":    1,
		"name":  "张三",
		"email": "<EMAIL>",
		"tags":  []string{"golang", "redis", "testing"},
		"metadata": map[string]interface{}{
			"created_at": "2023-01-01",
			"updated_at": "2023-12-31",
		},
	}

	// 设置复杂对象
	result := RedisManagerService.HSet(suite.ctx, suite.testKey, "user_info", user)
	assert.True(suite.T(), result, "应该能够设置复杂对象")

	// 获取复杂对象
	value, exists := RedisManagerService.HGet(suite.ctx, suite.testKey, "user_info")
	assert.True(suite.T(), exists, "复杂对象应该存在")

	// 验证复杂对象的结构（JSON反序列化后的结构）
	userMap, ok := value.(map[string]interface{})
	assert.True(suite.T(), ok, "应该能够转换为map")
	assert.Equal(suite.T(), float64(1), userMap["id"], "id字段应该正确")
	assert.Equal(suite.T(), "张三", userMap["name"], "name字段应该正确")
	assert.Equal(suite.T(), "<EMAIL>", userMap["email"], "email字段应该正确")
}

// TestConcurrentOperations 测试并发操作
func (suite *RedisHashTestSuite) TestConcurrentOperations() {
	// 这个测试验证在并发环境下HASH操作的正确性
	done := make(chan bool, 10)

	// 启动10个goroutine并发执行HIncrBy操作
	for i := 0; i < 10; i++ {
		go func(id int) {
			for j := 0; j < 10; j++ {
				RedisManagerService.HIncrBy(suite.ctx, suite.testKey, "concurrent_counter", 1)
			}
			done <- true
		}(i)
	}

	// 等待所有goroutine完成
	for i := 0; i < 10; i++ {
		<-done
	}

	// 验证最终结果
	value, exists := RedisManagerService.HGet(suite.ctx, suite.testKey, "concurrent_counter")
	assert.True(suite.T(), exists, "并发计数器应该存在")
	assert.Equal(suite.T(), float64(100), value, "并发操作后计数器应该为100")
}

// TestRunner 运行测试套件
func TestRedisHashTestSuite(t *testing.T) {
	suite.Run(t, new(RedisHashTestSuite))
}
