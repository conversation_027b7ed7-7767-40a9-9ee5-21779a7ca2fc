package redisManager

import (
	"context"
	"fmt"
	"testing"
)

// 简单的测试函数，不依赖外部测试框架
func TestHashOperationsSimple(t *testing.T) {
	// 跳过测试如果没有Redis连接
	if Redis == nil {
		t.Skip("Redis not initialized, skipping hash operations test")
		return
	}

	ctx := context.Background()
	testKey := "test_hash_simple"

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)

	t.<PERSON>("HSet and HGet", func(t *testing.T) {
		// 测试HSet
		result := RedisManagerService.HSet(ctx, testKey, "name", "张三")
		if !result {
			t.Errorf("HSet should return true for new field, got %v", result)
		}

		// 测试HGet
		value, exists := RedisManagerService.HGet(ctx, testKey, "name")
		if !exists {
			t.Error("HGet should return true for existing field")
		}
		if value != "张三" {
			t.<PERSON><PERSON><PERSON>("Expected '张三', got %v", value)
		}

		// 测试获取不存在的字段
		_, exists = RedisManagerService.HGet(ctx, testKey, "nonexistent")
		if exists {
			t.Error("HGet should return false for non-existent field")
		}
	})

	t.Run("HMSet and HMGet", func(t *testing.T) {
		// 测试HMSet
		fields := map[string]interface{}{
			"age":  25,
			"city": "北京",
		}
		result := RedisManagerService.HMSet(ctx, testKey, fields)
		if !result {
			t.Errorf("HMSet should return true, got %v", result)
		}

		// 测试HMGet
		values := RedisManagerService.HMGet(ctx, testKey, "name", "age", "city")
		if len(values) != 3 {
			t.Errorf("Expected 3 values, got %d", len(values))
		}
		if values[0] != "张三" {
			t.Errorf("Expected '张三', got %v", values[0])
		}
		if values[1] != float64(25) {
			t.Errorf("Expected 25, got %v", values[1])
		}
		if values[2] != "北京" {
			t.Errorf("Expected '北京', got %v", values[2])
		}
	})

	t.Run("HGetAll", func(t *testing.T) {
		// 测试HGetAll
		allFields := RedisManagerService.HGetAll(ctx, testKey)
		if len(allFields) < 3 {
			t.Errorf("Expected at least 3 fields, got %d", len(allFields))
		}
		if allFields["name"] != "张三" {
			t.Errorf("Expected name='张三', got %v", allFields["name"])
		}
	})

	t.Run("HExists", func(t *testing.T) {
		// 测试存在的字段
		exists := RedisManagerService.HExists(ctx, testKey, "name")
		if !exists {
			t.Error("HExists should return true for existing field")
		}

		// 测试不存在的字段
		exists = RedisManagerService.HExists(ctx, testKey, "nonexistent")
		if exists {
			t.Error("HExists should return false for non-existent field")
		}
	})

	t.Run("HKeys and HVals", func(t *testing.T) {
		// 测试HKeys
		keys := RedisManagerService.HKeys(ctx, testKey)
		if len(keys) < 3 {
			t.Errorf("Expected at least 3 keys, got %d", len(keys))
		}

		// 测试HVals
		values := RedisManagerService.HVals(ctx, testKey)
		if len(values) < 3 {
			t.Errorf("Expected at least 3 values, got %d", len(values))
		}
	})

	t.Run("HLen", func(t *testing.T) {
		// 测试HLen
		length := RedisManagerService.HLen(ctx, testKey)
		if length < 3 {
			t.Errorf("Expected at least 3 fields, got %d", length)
		}
	})

	t.Run("HIncrBy", func(t *testing.T) {
		// 测试HIncrBy
		result := RedisManagerService.HIncrBy(ctx, testKey, "counter", 5)
		if result != 5 {
			t.Errorf("Expected 5, got %d", result)
		}

		result = RedisManagerService.HIncrBy(ctx, testKey, "counter", 3)
		if result != 8 {
			t.Errorf("Expected 8, got %d", result)
		}
	})

	t.Run("HIncrByFloat", func(t *testing.T) {
		// 测试HIncrByFloat
		result := RedisManagerService.HIncrByFloat(ctx, testKey, "score", 3.14)
		if result != 3.14 {
			t.Errorf("Expected 3.14, got %f", result)
		}

		result = RedisManagerService.HIncrByFloat(ctx, testKey, "score", 2.86)
		if result != 6.0 {
			t.Errorf("Expected 6.0, got %f", result)
		}
	})

	t.Run("HDel", func(t *testing.T) {
		// 测试HDel
		deletedCount := RedisManagerService.HDel(ctx, testKey, "counter")
		if deletedCount != 1 {
			t.Errorf("Expected 1 deleted field, got %d", deletedCount)
		}

		// 验证字段已被删除
		exists := RedisManagerService.HExists(ctx, testKey, "counter")
		if exists {
			t.Error("Deleted field should not exist")
		}
	})

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)
}

// 测试复杂对象存储
func TestHashComplexObjects(t *testing.T) {
	if Redis == nil {
		t.Skip("Redis not initialized, skipping complex objects test")
		return
	}

	ctx := context.Background()
	testKey := "test_hash_complex"

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)

	// 测试复杂对象
	user := map[string]interface{}{
		"id":    1001,
		"name":  "李四",
		"email": "<EMAIL>",
		"tags":  []string{"golang", "redis"},
		"metadata": map[string]interface{}{
			"created_at": "2023-01-01",
			"version":    "1.0",
		},
	}

	// 存储复杂对象
	result := RedisManagerService.HSet(ctx, testKey, "user_profile", user)
	if !result {
		t.Error("HSet should return true for complex object")
	}

	// 获取复杂对象
	value, exists := RedisManagerService.HGet(ctx, testKey, "user_profile")
	if !exists {
		t.Error("Complex object should exist")
	}

	// 验证复杂对象结构
	userMap, ok := value.(map[string]interface{})
	if !ok {
		t.Error("Should be able to convert to map")
	}

	if userMap["id"] != float64(1001) {
		t.Errorf("Expected id=1001, got %v", userMap["id"])
	}

	if userMap["name"] != "李四" {
		t.Errorf("Expected name='李四', got %v", userMap["name"])
	}

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)
}

// 基准测试示例
func BenchmarkHashOperationsSimple(b *testing.B) {
	if Redis == nil {
		b.Skip("Redis not initialized, skipping benchmark")
		return
	}

	ctx := context.Background()
	testKey := "bench_hash_simple"

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		field := fmt.Sprintf("field_%d", i)
		value := fmt.Sprintf("value_%d", i)
		RedisManagerService.HSet(ctx, testKey, field, value)
	}

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)
}

// 演示函数，展示如何使用Hash操作
func ExampleHashOperations() {
	if Redis == nil {
		fmt.Println("Redis not initialized")
		return
	}

	ctx := context.Background()
	userKey := "user:demo"

	// 清理演示数据
	RedisManagerService.Del(ctx, userKey)

	fmt.Println("=== Redis Hash操作演示 ===")

	// 1. 设置用户信息
	RedisManagerService.HSet(ctx, userKey, "name", "演示用户")
	RedisManagerService.HSet(ctx, userKey, "age", 30)
	RedisManagerService.HSet(ctx, userKey, "email", "<EMAIL>")

	// 2. 获取用户信息
	name, _ := RedisManagerService.HGet(ctx, userKey, "name")
	age, _ := RedisManagerService.HGet(ctx, userKey, "age")
	fmt.Printf("用户: %v, 年龄: %v\n", name, age)

	// 3. 批量设置
	fields := map[string]interface{}{
		"city":   "上海",
		"phone":  "13800138000",
		"active": true,
	}
	RedisManagerService.HMSet(ctx, userKey, fields)

	// 4. 获取所有信息
	allInfo := RedisManagerService.HGetAll(ctx, userKey)
	fmt.Println("用户完整信息:")
	for field, value := range allInfo {
		fmt.Printf("  %s: %v\n", field, value)
	}

	// 5. 计数器操作
	loginCount := RedisManagerService.HIncrBy(ctx, userKey, "login_count", 1)
	fmt.Printf("登录次数: %d\n", loginCount)

	// 6. 清理演示数据
	RedisManagerService.Del(ctx, userKey)
	fmt.Println("演示完成，数据已清理")
}
