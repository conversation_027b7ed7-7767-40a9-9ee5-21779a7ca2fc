package redisManager

import (
	"context"
	"fmt"
	"testing"
)

// BenchmarkHSet 基准测试HSet操作
func BenchmarkHSet(b *testing.B) {
	ctx := context.Background()
	testKey := "bench_hash_set"

	// 初始化Redis连接（如果还没有初始化）
	if Redis == nil {
		config := &RedisConfig{
			Host:     "localhost",
			Port:     "6379",
			Password: "",
			Db:       1,
			Prefix:   "bench_",
		}
		InitRedis(config)
	}

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)

	b.ResetTimer()
	b.Run<PERSON>ara<PERSON>l(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			field := fmt.Sprintf("field_%d", i)
			value := fmt.Sprintf("value_%d", i)
			RedisManagerService.HSet(ctx, testKey, field, value)
			i++
		}
	})

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)
}

// BenchmarkHGet 基准测试HGet操作
func BenchmarkHGet(b *testing.B) {
	ctx := context.Background()
	testKey := "bench_hash_get"

	// 初始化Redis连接（如果还没有初始化）
	if Redis == nil {
		config := &RedisConfig{
			Host:     "localhost",
			Port:     "6379",
			Password: "",
			Db:       1,
			Prefix:   "bench_",
		}
		InitRedis(config)
	}

	// 预先设置一些测试数据
	for i := 0; i < 1000; i++ {
		field := fmt.Sprintf("field_%d", i)
		value := fmt.Sprintf("value_%d", i)
		RedisManagerService.HSet(ctx, testKey, field, value)
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			field := fmt.Sprintf("field_%d", i%1000)
			RedisManagerService.HGet(ctx, testKey, field)
			i++
		}
	})

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)
}

// BenchmarkHGetAll 基准测试HGetAll操作
func BenchmarkHGetAll(b *testing.B) {
	ctx := context.Background()
	testKey := "bench_hash_getall"

	// 初始化Redis连接（如果还没有初始化）
	if Redis == nil {
		config := &RedisConfig{
			Host:     "localhost",
			Port:     "6379",
			Password: "",
			Db:       1,
			Prefix:   "bench_",
		}
		InitRedis(config)
	}

	// 预先设置测试数据
	for i := 0; i < 100; i++ {
		field := fmt.Sprintf("field_%d", i)
		value := fmt.Sprintf("value_%d", i)
		RedisManagerService.HSet(ctx, testKey, field, value)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		RedisManagerService.HGetAll(ctx, testKey)
	}

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)
}

// BenchmarkHMSet 基准测试HMSet操作
func BenchmarkHMSet(b *testing.B) {
	ctx := context.Background()
	testKey := "bench_hash_mset"

	// 初始化Redis连接（如果还没有初始化）
	if Redis == nil {
		config := &RedisConfig{
			Host:     "localhost",
			Port:     "6379",
			Password: "",
			Db:       1,
			Prefix:   "bench_",
		}
		InitRedis(config)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		fields := make(map[string]interface{})
		for j := 0; j < 10; j++ {
			field := fmt.Sprintf("field_%d_%d", i, j)
			value := fmt.Sprintf("value_%d_%d", i, j)
			fields[field] = value
		}
		RedisManagerService.HMSet(ctx, testKey, fields)
	}

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)
}

// BenchmarkHMGet 基准测试HMGet操作
func BenchmarkHMGet(b *testing.B) {
	ctx := context.Background()
	testKey := "bench_hash_mget"

	// 初始化Redis连接（如果还没有初始化）
	if Redis == nil {
		config := &RedisConfig{
			Host:     "localhost",
			Port:     "6379",
			Password: "",
			Db:       1,
			Prefix:   "bench_",
		}
		InitRedis(config)
	}

	// 预先设置测试数据
	fields := make(map[string]interface{})
	for i := 0; i < 100; i++ {
		field := fmt.Sprintf("field_%d", i)
		value := fmt.Sprintf("value_%d", i)
		fields[field] = value
	}
	RedisManagerService.HMSet(ctx, testKey, fields)

	// 准备要获取的字段列表
	getFields := make([]string, 10)
	for i := 0; i < 10; i++ {
		getFields[i] = fmt.Sprintf("field_%d", i)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		RedisManagerService.HMGet(ctx, testKey, getFields...)
	}

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)
}

// BenchmarkHIncrBy 基准测试HIncrBy操作
func BenchmarkHIncrBy(b *testing.B) {
	ctx := context.Background()
	testKey := "bench_hash_incrby"

	// 初始化Redis连接（如果还没有初始化）
	if Redis == nil {
		config := &RedisConfig{
			Host:     "localhost",
			Port:     "6379",
			Password: "",
			Db:       1,
			Prefix:   "bench_",
		}
		InitRedis(config)
	}

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			field := fmt.Sprintf("counter_%d", i%10)
			RedisManagerService.HIncrBy(ctx, testKey, field, 1)
			i++
		}
	})

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)
}

// BenchmarkHIncrByFloat 基准测试HIncrByFloat操作
func BenchmarkHIncrByFloat(b *testing.B) {
	ctx := context.Background()
	testKey := "bench_hash_incrbyfloat"

	// 初始化Redis连接（如果还没有初始化）
	if Redis == nil {
		config := &RedisConfig{
			Host:     "localhost",
			Port:     "6379",
			Password: "",
			Db:       1,
			Prefix:   "bench_",
		}
		InitRedis(config)
	}

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			field := fmt.Sprintf("score_%d", i%10)
			RedisManagerService.HIncrByFloat(ctx, testKey, field, 1.5)
			i++
		}
	})

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)
}

// BenchmarkComplexObjectHSet 基准测试复杂对象的HSet操作
func BenchmarkComplexObjectHSet(b *testing.B) {
	ctx := context.Background()
	testKey := "bench_hash_complex"

	// 初始化Redis连接（如果还没有初始化）
	if Redis == nil {
		config := &RedisConfig{
			Host:     "localhost",
			Port:     "6379",
			Password: "",
			Db:       1,
			Prefix:   "bench_",
		}
		InitRedis(config)
	}

	// 准备复杂对象
	complexObject := map[string]interface{}{
		"id":    12345,
		"name":  "测试用户",
		"email": "<EMAIL>",
		"tags":  []string{"golang", "redis", "testing", "performance"},
		"metadata": map[string]interface{}{
			"created_at": "2023-01-01T00:00:00Z",
			"updated_at": "2023-12-31T23:59:59Z",
			"version":    "1.0.0",
		},
		"settings": map[string]interface{}{
			"theme":         "dark",
			"language":      "zh-CN",
			"notifications": true,
		},
	}

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		field := fmt.Sprintf("user_%d", i)
		RedisManagerService.HSet(ctx, testKey, field, complexObject)
	}

	// 清理测试数据
	RedisManagerService.Del(ctx, testKey)
}
