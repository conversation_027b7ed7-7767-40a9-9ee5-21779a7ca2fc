# Redis Hash操作测试指南

本文档介绍如何运行Redis Hash操作的测试用例和基准测试。

## 前置条件

1. **Redis服务器**: 确保Redis服务器正在运行
   ```bash
   # 启动Redis服务器（根据你的安装方式）
   redis-server
   
   # 或者使用Docker
   docker run -d -p 6379:6379 redis:latest
   ```

2. **Go环境**: 确保Go 1.22.5或更高版本已安装

3. **依赖包**: 确保所有依赖包已安装
   ```bash
   go mod tidy
   ```

## 测试文件说明

### 1. 功能测试 (`redis_manager_hash_test.go`)

包含完整的Hash操作功能测试，覆盖以下操作：

- **基本操作**: HSet, HGet, HGetAll, HDel, HExists
- **查询操作**: HKeys, HVals, HLen
- **数值操作**: HIncrBy, HIncrByFloat
- **批量操作**: HMSet, HMGet
- **复杂数据类型**: 对象序列化/反序列化
- **并发操作**: 并发安全性测试

### 2. 基准测试 (`redis_manager_hash_bench_test.go`)

包含性能基准测试，用于评估各种Hash操作的性能：

- HSet性能测试
- HGet性能测试
- HGetAll性能测试
- HMSet批量设置性能测试
- HMGet批量获取性能测试
- HIncrBy数值增量性能测试
- HIncrByFloat浮点增量性能测试
- 复杂对象存储性能测试

### 3. 示例代码 (`examples/hash_operations_example.go`)

提供实际使用场景的示例代码，包括：

- 用户信息管理
- 会话管理
- 购物车管理
- 统计计数器
- 复杂对象存储

## 运行测试

### 运行功能测试

```bash
# 进入redisManager目录
cd src/pkg/cache/redisManager

# 运行所有Hash操作测试
go test -v -run TestRedisHashTestSuite

# 运行特定测试
go test -v -run TestRedisHashTestSuite/TestHSet
go test -v -run TestRedisHashTestSuite/TestHGet
go test -v -run TestRedisHashTestSuite/TestHMSet
```

### 运行基准测试

```bash
# 运行所有基准测试
go test -bench=BenchmarkH -benchmem

# 运行特定基准测试
go test -bench=BenchmarkHSet -benchmem
go test -bench=BenchmarkHGet -benchmem
go test -bench=BenchmarkHMSet -benchmem

# 运行基准测试并生成性能报告
go test -bench=BenchmarkH -benchmem -cpuprofile=cpu.prof -memprofile=mem.prof
```

### 运行示例代码

```bash
# 创建一个简单的main.go文件来运行示例
cat > test_hash_example.go << 'EOF'
package main

import "gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager/examples"

func main() {
    examples.RunHashExample()
}
EOF

# 运行示例
go run test_hash_example.go
```

## 测试配置

### Redis连接配置

测试使用以下默认配置：

```go
config := &RedisConfig{
    Host:     "localhost",
    Port:     "6379", 
    Password: "",
    Db:       1,        // 使用数据库1进行测试
    Prefix:   "test_",  // 测试数据前缀
}
```

### 修改测试配置

如果需要修改Redis连接配置，请编辑测试文件中的配置部分：

```go
// 在测试文件中找到SetupSuite方法并修改配置
config := &RedisConfig{
    Host:     "your-redis-host",
    Port:     "your-redis-port",
    Password: "your-redis-password",
    Db:       1,
    Prefix:   "test_",
}
```

## 测试覆盖率

查看测试覆盖率：

```bash
# 生成覆盖率报告
go test -coverprofile=coverage.out

# 查看覆盖率详情
go tool cover -html=coverage.out

# 查看覆盖率统计
go tool cover -func=coverage.out
```

## 性能基准参考

在标准测试环境下的性能参考（仅供参考，实际性能取决于硬件和网络环境）：

```
BenchmarkHSet-8                    50000    30000 ns/op    500 B/op    10 allocs/op
BenchmarkHGet-8                   100000    15000 ns/op    300 B/op     8 allocs/op
BenchmarkHGetAll-8                 10000   150000 ns/op   5000 B/op    50 allocs/op
BenchmarkHMSet-8                   20000    80000 ns/op   2000 B/op    25 allocs/op
BenchmarkHMGet-8                   30000    50000 ns/op   1500 B/op    20 allocs/op
BenchmarkHIncrBy-8                 80000    20000 ns/op    400 B/op     8 allocs/op
BenchmarkHIncrByFloat-8            70000    25000 ns/op    450 B/op     9 allocs/op
BenchmarkComplexObjectHSet-8       20000    70000 ns/op   3000 B/op    30 allocs/op
```

## 故障排除

### 常见问题

1. **连接失败**: 
   - 检查Redis服务是否运行
   - 验证连接配置（主机、端口、密码）
   - 检查防火墙设置

2. **测试超时**:
   - 检查网络延迟
   - 增加测试超时时间
   - 检查Redis服务器性能

3. **权限错误**:
   - 检查Redis密码配置
   - 验证数据库访问权限

### 调试技巧

1. **启用详细日志**:
   ```bash
   go test -v -run TestRedisHashTestSuite 2>&1 | tee test.log
   ```

2. **单独测试每个操作**:
   ```bash
   go test -v -run TestRedisHashTestSuite/TestHSet
   ```

3. **检查Redis状态**:
   ```bash
   redis-cli ping
   redis-cli info
   ```

## 贡献指南

如果你想为Hash操作测试贡献代码：

1. 确保所有测试通过
2. 添加适当的测试用例覆盖新功能
3. 更新基准测试以包含新操作
4. 更新文档和示例

## 相关文档

- [Redis Hash Commands](https://redis.io/commands/?group=hash)
- [Go Redis Client Documentation](https://pkg.go.dev/github.com/go-redis/redis/v8)
- [Go Testing Package](https://pkg.go.dev/testing)
- [Go Benchmark Guide](https://pkg.go.dev/testing#hdr-Benchmarks)
