package examples

import (
	"context"
	"fmt"
	"log"

	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager"
)

// User 用户结构体示例
type User struct {
	ID       int                    `json:"id"`
	Name     string                 `json:"name"`
	Email    string                 `json:"email"`
	Age      int                    `json:"age"`
	Tags     []string               `json:"tags"`
	Settings map[string]interface{} `json:"settings"`
}

// HashOperationsExample Redis Hash操作示例
func HashOperationsExample() {
	// 初始化Redis连接
	config := &redisManager.RedisConfig{
		Host:     "localhost",
		Port:     "6379",
		Password: "",
		Db:       0,
		Prefix:   "example_",
	}
	redisManager.InitRedis(config)

	ctx := context.Background()

	fmt.Println("=== Redis Hash操作示例 ===")

	// 1. 基本的HSet和HGet操作
	fmt.Println("\n1. 基本的HSet和HGet操作:")
	userKey := "user:1001"

	// 设置用户基本信息
	redisManager.RedisManagerService.HSet(ctx, userKey, "name", "张三")
	redisManager.RedisManagerService.HSet(ctx, userKey, "email", "<EMAIL>")
	redisManager.RedisManagerService.HSet(ctx, userKey, "age", 28)

	// 获取用户信息
	name, exists := redisManager.RedisManagerService.HGet(ctx, userKey, "name")
	if exists {
		fmt.Printf("用户姓名: %v\n", name)
	}

	age, exists := redisManager.RedisManagerService.HGet(ctx, userKey, "age")
	if exists {
		fmt.Printf("用户年龄: %v\n", age)
	}

	// 2. 批量操作示例
	fmt.Println("\n2. 批量操作示例:")

	// 使用HMSet批量设置多个字段
	userFields := map[string]interface{}{
		"phone":   "13800138000",
		"city":    "北京",
		"company": "科技公司",
		"salary":  15000.50,
		"married": true,
	}
	redisManager.RedisManagerService.HMSet(ctx, userKey, userFields)

	// 使用HMGet批量获取多个字段
	values := redisManager.RedisManagerService.HMGet(ctx, userKey, "name", "phone", "city", "salary")
	fmt.Printf("批量获取结果: 姓名=%v, 电话=%v, 城市=%v, 薪资=%v\n", values[0], values[1], values[2], values[3])

	// 3. 获取所有字段和值
	fmt.Println("\n3. 获取所有字段和值:")
	allFields := redisManager.RedisManagerService.HGetAll(ctx, userKey)
	fmt.Println("用户所有信息:")
	for field, value := range allFields {
		fmt.Printf("  %s: %v\n", field, value)
	}

	// 4. 字段存在性检查
	fmt.Println("\n4. 字段存在性检查:")
	exists = redisManager.RedisManagerService.HExists(ctx, userKey, "email")
	fmt.Printf("email字段存在: %v\n", exists)

	exists = redisManager.RedisManagerService.HExists(ctx, userKey, "nonexistent")
	fmt.Printf("nonexistent字段存在: %v\n", exists)

	// 5. 获取字段名和值列表
	fmt.Println("\n5. 获取字段名和值列表:")
	keys := redisManager.RedisManagerService.HKeys(ctx, userKey)
	fmt.Printf("所有字段名: %v\n", keys)

	values = redisManager.RedisManagerService.HVals(ctx, userKey)
	fmt.Printf("所有值: %v\n", values)

	length := redisManager.RedisManagerService.HLen(ctx, userKey)
	fmt.Printf("字段数量: %d\n", length)

	// 6. 数值操作示例
	fmt.Println("\n6. 数值操作示例:")

	// 整数增量操作
	counterKey := "user:1001:stats"
	loginCount := redisManager.RedisManagerService.HIncrBy(ctx, counterKey, "login_count", 1)
	fmt.Printf("登录次数: %d\n", loginCount)

	loginCount = redisManager.RedisManagerService.HIncrBy(ctx, counterKey, "login_count", 5)
	fmt.Printf("增加5次后登录次数: %d\n", loginCount)

	// 浮点数增量操作
	score := redisManager.RedisManagerService.HIncrByFloat(ctx, counterKey, "score", 85.5)
	fmt.Printf("初始分数: %.2f\n", score)

	score = redisManager.RedisManagerService.HIncrByFloat(ctx, counterKey, "score", 12.3)
	fmt.Printf("增加12.3后分数: %.2f\n", score)

	// 7. 复杂对象存储示例
	fmt.Println("\n7. 复杂对象存储示例:")

	user := User{
		ID:    1001,
		Name:  "李四",
		Email: "<EMAIL>",
		Age:   30,
		Tags:  []string{"golang", "redis", "microservices"},
		Settings: map[string]interface{}{
			"theme":         "dark",
			"language":      "zh-CN",
			"notifications": true,
		},
	}

	// 存储复杂对象
	redisManager.RedisManagerService.HSet(ctx, "user:1002", "profile", user)

	// 获取复杂对象
	profile, exists := redisManager.RedisManagerService.HGet(ctx, "user:1002", "profile")
	if exists {
		fmt.Printf("用户档案: %v\n", profile)
	}

	// 8. 删除字段示例
	fmt.Println("\n8. 删除字段示例:")

	// 删除单个字段
	deletedCount := redisManager.RedisManagerService.HDel(ctx, userKey, "company")
	fmt.Printf("删除company字段，删除数量: %d\n", deletedCount)

	// 删除多个字段
	deletedCount = redisManager.RedisManagerService.HDel(ctx, userKey, "phone", "married")
	fmt.Printf("删除phone和married字段，删除数量: %d\n", deletedCount)

	// 验证删除结果
	length = redisManager.RedisManagerService.HLen(ctx, userKey)
	fmt.Printf("删除后字段数量: %d\n", length)

	// 9. 实际应用场景示例
	fmt.Println("\n9. 实际应用场景示例:")

	// 用户会话管理
	sessionKey := "session:abc123"
	sessionData := map[string]interface{}{
		"user_id":    1001,
		"username":   "zhangsan",
		"login_time": "2023-12-01 10:00:00",
		"ip_address": "*************",
		"user_agent": "Mozilla/5.0...",
	}
	redisManager.RedisManagerService.HMSet(ctx, sessionKey, sessionData)

	// 更新最后活动时间
	redisManager.RedisManagerService.HSet(ctx, sessionKey, "last_activity", "2023-12-01 10:30:00")

	// 获取会话信息
	userID, _ := redisManager.RedisManagerService.HGet(ctx, sessionKey, "user_id")
	lastActivity, _ := redisManager.RedisManagerService.HGet(ctx, sessionKey, "last_activity")
	fmt.Printf("会话用户ID: %v, 最后活动时间: %v\n", userID, lastActivity)

	// 购物车管理
	cartKey := "cart:user:1001"

	// 添加商品到购物车（商品ID作为字段，数量作为值）
	redisManager.RedisManagerService.HIncrBy(ctx, cartKey, "product:101", 2)
	redisManager.RedisManagerService.HIncrBy(ctx, cartKey, "product:102", 1)
	redisManager.RedisManagerService.HIncrBy(ctx, cartKey, "product:103", 3)

	// 获取购物车所有商品
	cartItems := redisManager.RedisManagerService.HGetAll(ctx, cartKey)
	fmt.Println("购物车商品:")
	for productID, quantity := range cartItems {
		fmt.Printf("  %s: %v件\n", productID, quantity)
	}

	// 获取购物车商品总数
	cartSize := redisManager.RedisManagerService.HLen(ctx, cartKey)
	fmt.Printf("购物车商品种类数: %d\n", cartSize)

	// 10. 清理示例数据
	fmt.Println("\n10. 清理示例数据:")
	redisManager.RedisManagerService.Del(ctx, userKey)
	redisManager.RedisManagerService.Del(ctx, counterKey)
	redisManager.RedisManagerService.Del(ctx, "user:1002")
	redisManager.RedisManagerService.Del(ctx, sessionKey)
	redisManager.RedisManagerService.Del(ctx, cartKey)
	fmt.Println("示例数据已清理完成")
}

// RunHashExample 运行Hash操作示例
func RunHashExample() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("示例运行出错: %v", r)
			log.Println("请确保Redis服务正在运行，并且连接配置正确")
		}
	}()

	HashOperationsExample()
}
