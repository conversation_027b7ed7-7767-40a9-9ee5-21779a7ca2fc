package alarm

import (
	"fmt"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/helper"
	"path/filepath"
	"runtime"
	"strings"
)

type errorString struct {
	s string
}

type errorInfo struct {
	Time     string `json:"time"`
	Alarm    string `json:"alarm"`
	Message  string `json:"message"`
	Filename string `json:"filename"`
	Line     int    `json:"line"`
	Funcname string `json:"funcname"`
}

var AlarmEntity = &AlarmService{}

func InitAlarmServive(AlarmConfig *AlarmConfig) {
	AlarmEntity = &AlarmService{
		AlarmConfig: AlarmConfig,
	}
}

type AlarmService struct {
	AlarmConfig *AlarmConfig
}

// 告警方法
func (a *AlarmService) alarm(level string, str string, skip int, title string) {
	// 当前时间
	currentTime := helper.GetTimeStr()

	// 定义 文件名、行号、方法名
	fileName, line, helperName := "?", 0, "?"

	pc, fileName, line, ok := runtime.Caller(skip)

	if ok {
		helperName = runtime.FuncForPC(pc).Name()
		helperName = filepath.Ext(helperName)
		helperName = strings.TrimPrefix(helperName, ".")
	}

	//var msg = errorInfo{
	//	Time:     currentTime,
	//	Alarm:    level,
	//	Message:  str,
	//	Filename: fileName,
	//	Line:     line,
	//	Funcname: helperName,
	//}

	var text string
	if level == "SYSTEM" {

		trace := helper.PrintStackTrace()
		fmt.Printf("#错误信息: " + str)
		fmt.Println(trace)
		text = fmt.Sprintf(`### ⚠️ **报警信息**  
	
 **时间**: %s  
 **报警类型**: %s  
 **报警信息**: %s  
 **堆栈信息**: %s 

请尽快处理此问题，确保服务恢复正常运行！`, currentTime, level, str, trace)

	} else {

		text = fmt.Sprintf(`### ⚠️ **报警信息**  
	
 **时间**: %s  
 **报警类型**: %s  
 **报警信息**: %s  
 **文件**: %s  
 **行号**: %d  
 **函数名**: %s  

请尽快处理此问题，确保服务恢复正常运行！`, currentTime, level, str, fileName, line, helperName)

	}

	//jsons, errs := json.Marshal(msg)
	//
	//if errs != nil {
	//	log.AppLogger.Error("json marshal error:", zap.Any("err", errs))
	//}
	//
	//errorJsonInfo := string(jsons)

	//if level == "EMAIL" {
	//	// 执行发邮件
	//
	//} else if level == "SMS" {
	//	// 执行发短信
	//
	//} else if level == "WX" {
	//	// 执行发微信
	//	helper.SendToWeixin(a.AlarmConfig.DefaultWebhook, errorJsonInfo)
	//
	//} else if level == "INFO" {
	//	// 执行记日志
	//
	//} else if level == "PANIC" {
	//	// 执行PANIC方式
	//
	//} else if level == "DING" {
	// 执行PANIC方式
	if a.AlarmConfig.DefaultWebhook != "" {
		helper.SendToDingDing(a.AlarmConfig.DefaultWebhook, title, text)
	}
	//}
}

func (e *errorString) Error() string {
	return e.s
}

// 发钉钉 普通级别错误 无序打印堆栈信息
func DingDingNormal(text string, title string) {
	AlarmEntity.alarm("NORMAL", text, 2, title)
	return
}

// 发钉钉 接口级别错误
func DingDingSystem(text string, title string) {
	AlarmEntity.alarm("SYSTEM", text, 2, title)
	return
}
