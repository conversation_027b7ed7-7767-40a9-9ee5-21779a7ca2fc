package app

import (
	"fmt"
	"github.com/fvbock/endless"
	"github.com/gin-gonic/gin"
	"log"
)

type ServerConfig struct {
	Env      string `yaml:"env"`
	Port     string `yaml:"port"`
	UseHttps bool   `yaml:"use_https"`
	Cert     *Cert  `yaml:"cert"`
	IsDebug  bool   `yaml:"isDebug"`
	ServerId string `yaml:"server_id"`
}

type Cert struct {
	FilePath string `mapstructure:"file_path" json:"file_path"`
	KeyPath  string `mapstructure:"key_path" json:"key_path"`
}

type Server struct {
	Engine *gin.Engine

	Config *ServerConfig
}

// NewServer 创建一个服务
func (s *Server) NewServer(config *ServerConfig) *Server {
	return &Server{
		Config: config,
		Engine: s.Engine,
	}
}

// Start 开启一个服务
func (s *Server) Start() {

	if s.Config.IsDebug {
		gin.SetMode(gin.DebugMode)
	}
	fmt.Println("                                                                                                                                                                                               \n                                          ,----..                            ,----..             ____            ____      ,----..            ,--.          ,-.----.          ,--.             \n           .---.    ,---,.  ,----..      /   /   \\              ,----..     /   /   \\          ,'  , `.        ,'  , `.   /   /   \\         ,--.'|          \\    /  \\     ,--/  /|  ,----..    \n          /. ./|  ,'  .' | /   /   \\    /   .     :            /   /   \\   /   .     :      ,-+-,.' _ |     ,-+-,.' _ |  /   .     :    ,--,:  : |          |   :    \\ ,---,': / ' /   /   \\   \n      .--'.  ' ;,---.'   ||   :     :  .   /   ;.  \\    ,---,.|   :     : .   /   ;.  \\  ,-+-. ;   , ||  ,-+-. ;   , || .   /   ;.  \\,`--.'`|  ' :    ,---,.|   |  .\\ ::   : '/ / |   :     :  \n     /__./ \\ : ||   |   .'.   |  ;. / .   ;   /  ` ;  ,'  .' |.   |  ;. /.   ;   /  ` ; ,--.'|'   |  ;| ,--.'|'   |  ;|.   ;   /  ` ;|   :  :  | |  ,'  .' |.   :  |: ||   '   ,  .   |  ;. /  \n .--'.  '   \\' .:   :  |-,.   ; /--`  ;   |  ; \\ ; |,---.'   ,.   ; /--` ;   |  ; \\ ; ||   |  ,', |  ':|   |  ,', |  ':;   |  ; \\ ; |:   |   \\ | :,---.'   ,|   |   \\ :'   |  /   .   ; /--`   \n/___/ \\ |    ' ':   |  ;/|;   | ;  __ |   :  | ; | '|   |    |;   | ;    |   :  | ; | '|   | /  | |  |||   | /  | |  |||   :  | ; | '|   : '  '; ||   |    ||   : .   /|   ;  ;   ;   | ;  __  \n;   \\  \\;      :|   :   .'|   : |.' .'.   |  ' ' ' ::   :  .' |   : |    .   |  ' ' ' :'   | :  | :  |,'   | :  | :  |,.   |  ' ' ' :'   ' ;.    ;:   :  .' ;   | |`-' :   '   \\  |   : |.' .' \n \\   ;  `      ||   |  |-,.   | '_.' :'   ;  \\; /  |:   |.'   .   | '___ '   ;  \\; /  |;   . |  ; |--' ;   . |  ; |--' '   ;  \\; /  ||   | | \\   |:   |.'   |   | ;    |   |    ' .   | '_.' : \n  .   \\    .\\  ;'   :  ;/|'   ; : \\  | \\   \\  ',  / `---'     '   ; : .'| \\   \\  ',  / |   : |  | ,    |   : |  | ,     \\   \\  ',  / '   : |  ; .'`---'     :   ' |    '   : |.  \\'   ; : \\  | \n   \\   \\   ' \\ ||   |    \\'   | '/  .'  ;   :    /            '   | '/  :  ;   :    /  |   : '  |/     |   : '  |/       ;   :    /  |   | '`--'            :   : :    |   | '_\\.''   | '/  .' \n    :   '  |--\" |   :   .'|   :    /     \\   \\ .'             |   :    /    \\   \\ .'   ;   | |`-'      ;   | |`-'         \\   \\ .'   '   : |                |   | :    '   : |    |   :    /   \n     \\   \\ ;    |   | ,'   \\   \\ .'       `---`                \\   \\ .'      `---`     |   ;/          |   ;/              `---`     ;   |.'                `---'.|    ;   |,'     \\   \\ .'    \n      '---\"     `----'      `---`                               `---`                  '---'           '---'                         '---'                    `---`    '---'        `---`      \n                                                                                                                                                                                               ")
	port := s.Config.Port
	if s.Config.UseHttps {
		log.Println("当前配置走https")
		certFile := s.Config.Cert.FilePath
		certKeyFile := s.Config.Cert.FilePath
		if err := endless.ListenAndServeTLS(":"+port, certFile, certKeyFile, s.Engine); err != nil {
			log.Fatalf("listen: %s\n", err)
		}
	} else {
		gin.SetMode(gin.DebugMode)
		log.Println("当前配置走http")
		if err := endless.ListenAndServe(":"+port, s.Engine); err != nil {
			log.Fatalf("listen: %s\n", err)
		}
	}
}
