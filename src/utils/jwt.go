package utils

import (
	"fmt"
	"time"

	"github.com/dgrijalva/jwt-go"
)

// SecretKey 用于签名和验证JWT的密钥，应该保密
var SecretKey = []byte("betterWe")

// GenerateToken 生成JWT Token
func GenerateToken(userID string) (string, error) {
	// 创建一个新的Token对象，指定签名方法为HS256
	token := jwt.New(jwt.SigningMethodHS256)

	// 设置Token的Claims，这里设置了用户ID和过期时间
	claims := token.Claims.(jwt.MapClaims)
	claims["user_id"] = userID
	claims["exp"] = time.Now().Add(time.Hour * 24).Unix()

	// 签名Token并返回
	signedToken, err := token.SignedString(SecretKey)
	if err != nil {
		return "", err
	}

	return signedToken, nil
}

// ParseToken 解析JWT Token
func ParseToken(tokenString string) (*jwt.Token, error) {
	return jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法是否为HS256
		if method, ok := token.Method.(jwt.SigningMethod); !ok || method != jwt.SigningMethodHS256 {
			return nil, fmt.Errorf("无效的签名方法")
		}
		return SecretKey, nil
	})
}
