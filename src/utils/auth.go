package utils

import (
	"fmt"
	"sort"
	"strings"
)

func GenerateSign(params map[string]string, body string, appSecret string, sep string) string {
	finalStrs := make([]string, 0)
	sortedParamStr := sortParam(params, sep)
	if len(sortedParamStr) > 0 {
		finalStrs = append(finalStrs, sortedParamStr)
	}
	if len(body) > 0 {
		finalStrs = append(finalStrs, body)
	}
	finalStrs = append(finalStrs, appSecret)
	finalStr := strings.Join(finalStrs, "#")
	return GetMd5String(finalStr)
}

func sortParam(params map[string]string, sep string) string {
	if len(params) <= 0 {
		return ""
	}
	keys := make([]string, 0)
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	sortedStrs := make([]string, 0)
	for _, k := range keys {
		v := params[k]
		sortedStrs = append(sortedStrs, fmt.Sprintf("%s=%s", k, v))
	}
	return strings.Join(sortedStrs, sep)
}
