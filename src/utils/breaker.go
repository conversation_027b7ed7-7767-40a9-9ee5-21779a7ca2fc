package utils

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/sony/gobreaker"
	"sync"
	"time"
)

// CircuitBreakerManager 管理不同路由的熔断器
type CircuitBreakerManager struct {
	Breakers map[string]*gobreaker.CircuitBreaker
	mutex    sync.RWMutex
}

// 初始化熔断组件
func InitBreaker() *CircuitBreakerManager {
	// 创建限流管理器
	cbManager := NewCircuitBreakerManager()
	return cbManager
}

// NewCircuitBreakerManager 初始化熔断器管理器
func NewCircuitBreakerManager() *CircuitBreakerManager {
	return &CircuitBreakerManager{
		Breakers: make(map[string]*gobreaker.CircuitBreaker),
	}
}

// GetCircuitBreaker 获取或创建路由对应的熔断器
func (m *CircuitBreakerManager) GetCircuitBreaker(route string) *gobreaker.CircuitBreaker {
	// 创建新的熔断器
	cbSettings := gobreaker.Settings{
		Name: route,
		ReadyToTrip: func(counts gobreaker.Counts) bool {
			return counts.ConsecutiveFailures > 5
		},
		OnStateChange: func(name string, from, to gobreaker.State) {
			fmt.Printf("断路器 %s 状态从 %v 切换到 %v\n", name, from, to)
		},
		MaxRequests: 10,               // 每次状态转换时最多允许 10 个请求
		Interval:    30 * time.Second, // 30 秒内最多 10 次请求
		Timeout:     5 * time.Second,  // 熔断器打开后，等待 5 秒钟后恢复
	}
	return gobreaker.NewCircuitBreaker(cbSettings)
}

// 批量创建熔断器
func (m *CircuitBreakerManager) CreateCircuitBreakers(r *gin.Engine) {
	for _, route := range r.Routes() {
		// 打印每个路由的详细信息
		fmt.Printf("Method: %s, Path: %s\n", route.Method, route.Path)
		m.Breakers[route.Path] = m.GetCircuitBreaker(route.Path)
	}
}
