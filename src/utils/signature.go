package utils

import (
	"context"
	"errors"
	"fmt"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/db/mysql"
	"github.com/spf13/cast"
	"github.com/syyongx/php2go"
	"gorm.io/gorm"
	"sort"
	"strings"
	"time"
)

type Signature struct{}

var expiredTime int64 = 60

// 生成签名
func (this *Signature) Generate(headers map[string]any, params map[string]any, body string, secret string) (sign string, err error) {
	var headerSign = make(map[string]any, 3)
	headerSign["X-TIME"] = headers["X-TIME"]
	headerSign["X-EXPIRED"] = headers["X-EXPIRED"]
	headerSign["X-APPKEY"] = headers["X-APPKEY"]
	// 生成签名
	str := this.paramsToString(params, headerSign, body, secret)
	return php2go.Md5(str), nil

}

func (this *Signature) paramsToString(params map[string]any, headers map[string]any, body string, appSecret string) string {
	str1 := this.Implode(headers, "=", "&")
	str2 := this.Implode(params, "=", "&")
	str := str1 + str2 + body + appSecret

	return str
}

func (this *Signature) GetSecret(app_id string) (string, error) {
	cacheKey := this.GetSecretCacheKey(app_id)
	ctx := context.Background()
	secret := redisManager.RedisManagerService.Rmember(ctx, cacheKey, func() string {
		sct := GetAppSecret(app_id)
		return sct
	}, 60*60*24)
	return secret, nil
}

// 验证签名
func (this *Signature) Check(headers map[string]any, params map[string]any, bodyData string) error {
	// 参与签名的header字段
	var headerSign = make(map[string]any, 4)
	// 检查header中必须字段
	signature, erron := headers["X-SIGN"]
	if !erron || signature == "" {
		return errors.New("签名参数缺失")
	}
	sign := cast.ToString(signature)
	if sign == "" {
		return errors.New("签名参数缺失")
	}

	time1, erron := headers["X-TIME"]
	if !erron || time1 == "" {
		return errors.New("签名参数缺失")
	}
	time2 := cast.ToInt64(time1)
	if time2 <= 0 {
		return errors.New("签名参数错误")
	}
	headerSign["X-TIME"] = time2
	expired, erron := headers["X-EXPIRED"]
	if erron && expired != "" {
		expiredTime = cast.ToInt64(expired)
		headerSign["X-EXPIRED"] = expiredTime
	}

	// 检查是否是合法应用
	appid, erron := headers["X-APPKEY"]
	if !erron || appid == "" {
		return errors.New("APPKEY不存在")
	}
	appid1 := cast.ToString(appid)
	secret, e := this.GetSecret(appid1)
	if e != nil || secret == "" {
		return errors.New("非法应用访问")
	}
	headerSign["X-APPKEY"] = appid1

	// 验证时间是否过期
	now := time.Now()
	now1 := now.Unix()
	if now1 >= (time2 + expiredTime) {
		return errors.New("签名已经失效")
	}
	// 生成服务端签名
	serviceSign, err1 := this.Generate(headerSign, params, bodyData, secret)
	//fmt.Println("check_serviceSign:", serviceSign, "check_header:", headerSign, "check_params:", params, "check_bodyData:", bodyData)
	if err1 != nil {
		return err1
	}
	// 验证是否篡改
	if serviceSign != sign {
		return errors.New("非法访问")
	}

	return nil
}

// 对map的键按从小到大排序后，将map拼接成指定方式的字符串
func (this *Signature) Implode(m map[string]any, innerNeedle string, outerNeedle string) string {
	// 对map的key进行排序
	var keys []string
	for k := range m {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 拼接map
	var builder strings.Builder

	for _, mapKey := range keys {
		if builder.Len() > 0 {
			builder.WriteString(outerNeedle)
		}
		// 向builder中写入字符/字符串
		builder.Write([]byte(mapKey))
		builder.WriteString(innerNeedle)
		value := cast.ToString(m[mapKey])
		builder.WriteString(value)
	}
	// String() 方法获得拼接的字符串
	return builder.String()
}

func (s *Signature) GetSecretCacheKey(app_id string) string {
	return fmt.Sprintf("GetAppSecret:%s", app_id)
}

type BrandModel struct {
	ID         int64     `gorm:"column:id" db:"id" json:"id" form:"id"`
	Brand      string    `gorm:"column:brand" db:"brand" json:"brand" form:"brand"` //  品牌或者平台名称
	Appid      string    `gorm:"column:appid" db:"appid" json:"appid" form:"appid"`
	SecretKey  string    `gorm:"column:secret_key" db:"secret_key" json:"secret_key" form:"secret_key"`
	UpdateTime time.Time `gorm:"column:update_time" db:"update_time" json:"update_time" form:"update_time"`
	CreateTime time.Time `gorm:"column:create_time" db:"create_time" json:"create_time" form:"create_time"`
	Status     int64     `gorm:"column:status" db:"status" json:"status" form:"status"` //  1-正常 -1-删除 0-待定
	Desc       string    `gorm:"column:desc" db:"desc" json:"desc" form:"desc"`         //  描述
}

const (
	STATUS_OK  = 1
	STATUS_OFF = 0
)

func (this *BrandModel) TableName() string {
	return "weapp_brand"
}

func GetAppSecret(app_id string) string {
	brand := &BrandModel{}
	err := mysql.MysqlDb.Where(map[string]interface{}{"appid": app_id, "status": STATUS_OK}).First(brand).Error
	if err == nil {
		return brand.SecretKey
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		return ""
	} else {
		panic(err.Error())
	}
}
