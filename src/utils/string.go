package utils

import (
	"fmt"
	"reflect"
	"sort"
	"strings"

	"github.com/bytedance/sonic"
)

func JoinInt64Slice(int64Slice []int64, sep string, sorter func(i, j int) bool) string {
	if len(int64Slice) == 0 {
		return ""
	}
	if sorter != nil {
		sort.Slice(int64Slice, sorter)
	}
	int64Strs := make([]string, 0)
	for _, tagID := range int64Slice {
		int64Strs = append(int64Strs, fmt.Sprintf("%d", tagID))
	}
	result := strings.Join(int64Strs, sep)
	return result
}

func UnmarshalWithType(data []byte, tp reflect.Type) (interface{}, error) {
	result := reflect.New(tp).Interface()
	err := sonic.Unmarshal(data, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func DistinctStrings(strs []string) []string {
	strMap := make(map[string]bool)
	for _, str := range strs {
		strMap[str] = true
	}
	result := make([]string, 0)
	for str := range strMap {
		result = append(result, str)
	}
	return result
}

// DiffStrings 找出在 strs1 但不在 strs2 中的数据
func DiffStrings(strs1 []string, strs2 []string) []string {
	strMap := make(map[string]bool)
	for _, str := range strs1 {
		strMap[str] = true
	}
	for _, str := range strs2 {
		delete(strMap, str)
	}
	result := make([]string, 0)
	for str := range strMap {
		result = append(result, str)
	}
	return result
}

func Convert2Strings[T any](ts []T) []string {
	result := make([]string, 0)
	for _, t := range ts {
		result = append(result, fmt.Sprint(t))
	}
	return result
}

func ContainsString(strs []string, str string) bool {
	for _, s := range strs {
		if s == str {
			return true
		}
	}
	return false
}

func ContainsAnyStrings(strs []string, strs2 []string) bool {
	strMap := make(map[string]bool)
	for _, str := range strs {
		strMap[str] = true
	}
	for _, str := range strs2 {
		if strMap[str] {
			return true
		}
	}
	return false
}

// Contains 检查元素是否存在于切片中
func Contains[T comparable](slice []T, elem T) bool {
	for _, v := range slice {
		if v == elem {
			return true
		}
	}
	return false
}
