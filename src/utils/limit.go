package utils

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/app"
	"github.com/gin-gonic/gin"
	"github.com/ulule/limiter/v3"
	"github.com/ulule/limiter/v3/drivers/store/memory"
	"log"
	"net/http"
	"sync"
)

type LimiterConfig struct {
	GlobalQPS string `yaml:"global_qps"`
}

type RouteLimiterManager struct {
	mu            sync.RWMutex
	globalLimiter *limiter.Limiter
	routeLimiters map[string]*limiter.Limiter
}

// 初始化限流器
func InitLimiter(globalQps string) *RouteLimiterManager {
	// 创建限流管理器
	manager, err := NewRouteLimiterManager(globalQps) // 全局限流：每 10 秒允许 10 次请求
	if err != nil {
		log.Fatalf("Failed to initialize limiter manager: %v", err)
	}
	return manager
}

// NewRouteLimiterManager initializes a limiter manager
func NewRouteLimiterManager(globalRate string) (*RouteLimiterManager, error) {
	store := memory.NewStore()

	// Create a global limiter
	globalRateLimit, err := limiter.NewRateFromFormatted(globalRate) // Example: "5-S"
	if err != nil {
		return nil, err
	}
	globalLimiter := limiter.New(store, globalRateLimit)

	return &RouteLimiterManager{
		globalLimiter: globalLimiter,
		routeLimiters: make(map[string]*limiter.Limiter),
	}, nil
}

// GetLimiter retrieves the limiter for a specific route
func (m *RouteLimiterManager) GetLimiter(route string) *limiter.Limiter {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if limiter, exists := m.routeLimiters[route]; exists {
		return limiter
	}
	return m.globalLimiter
}

// SetRouteLimiter sets a specific limiter for a route
func (m *RouteLimiterManager) SetRouteLimiter(route string, rate string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	rateLimit, err := limiter.NewRateFromFormatted(rate) // Example: "2-S"
	if err != nil {
		return err
	}
	store := memory.NewStore()
	m.routeLimiters[route] = limiter.New(store, rateLimit)
	return nil
}

func CustomErrorHandler(c *gin.Context) {
	app.SetError(c, "请求超限!", http.StatusTooManyRequests)
	return
}
