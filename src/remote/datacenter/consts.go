package datacenter

import "gitee.com/jianmengkeji/wego-common-pkg/src/consts"

type Source string

const (
	// btw、shop、gym3
	SourceBTW  Source = "btw"
	SourceShop Source = "shop"
	SourceGYM3 Source = "gym3"
	SourceTag  Source = "tag"
)

func (s Source) String() string {
	return string(s)
}

func ConvertBusinessType2Source(b consts.BusinessType) Source {
	switch b {
	case consts.BusinessTypeBetterWe:
		return SourceBTW
	case consts.BusinessTypeGYM3:
		return SourceGYM3
	case consts.BusinessTypeShoppingMall:
		return SourceShop
	default:
		return ""
	}
}
