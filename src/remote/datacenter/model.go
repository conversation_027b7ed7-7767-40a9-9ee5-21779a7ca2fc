package datacenter

type Resp[T any] struct {
	Code  int    `json:"code"`
	Msg   string `json:"msg"`
	Time  int    `json:"time"`
	Data  T      `json:"data"`
	Error string `json:"error"`
}

type CenterUser struct {
	ID         int64     `json:"id"`
	AreaCode   int       `json:"areacode"`
	Mobile     string    `json:"mobile"`
	SourceUIDs SourceUID `json:"source_uids"`
}

type SourceUID struct {
	GYM3 int64 `json:"gym3"`
	BTW  int64 `json:"btw"`
	Shop int64 `json:"shop"`
}
