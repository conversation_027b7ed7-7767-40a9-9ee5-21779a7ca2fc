package datacenter

type GetCenterUIDByBizUIDsReq struct {
	UIDs       []int64 `json:"from_source_uids"`
	ToSource   Source  `json:"to_source,omitempty"`
	FromSource Source  `json:"from_source,omitempty"`
}

type GetCenterUIDByUIDsReq struct {
	UIDs     []int64 `json:"uids"`
	ToSource Source  `json:"to_source,omitempty"`
}

// Request
type UidIsInPackage struct {
	// 人群包idList
	PackageIDList []int `json:"packageIdList"`
	// 用户中心id
	UcenterID int64 `json:"ucenterId"`
	// 调用方业务线1-BetterWe, 2-健萌, 3-商城'
	BusinessLine int64 `json:"businessLine"`
}
