package datacenter

import (
	"reflect"
	"time"

	"gitee.com/jianmengkeji/wego-common-pkg/src/consts"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote/base"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote/common"
)

var DataCenterService base.IRemoteService

type ServiceConfig struct {
	BaseURL   string `mapstructure:"base_url" json:"base_url" yaml:"base_url"`
	AppID     string `mapstructure:"app_id" json:"app_id" yaml:"app_id"`
	AppSecret string `mapstructure:"app_secret" json:"app_secret" yaml:"app_secret"`
}

func NewUserCenterService(config *ServiceConfig) base.IRemoteService {
	remoteService := base.NewRemoteService(consts.UserCenterService, config.BaseURL)
	getUserBySourceUIDsCaller := base.NewHttpServiceCallerBuilder().
		WithUrl("ucenter/user/getUcenterUserBySourceUids").
		WithMethod(consts.HttpMethodPost).
		WithTimeout(3 * time.Second).
		WithResultType(reflect.TypeOf(Resp[map[string]*CenterUser]{})).
		WithContentType(consts.HttpContentTypeJson).
		Build()
	remoteService.RegisterCaller(consts.GetCenterUserBySourceUIDs, getUserBySourceUIDsCaller, []base.InterfaceFunc{common.AddJavaAuthSign(config.AppID, config.AppSecret)})

	// 获取用户信息
	getUserByUIDsCaller := base.NewHttpServiceCallerBuilder().
		WithUrl("ucenter/user/getUcenterUserByUids").
		WithMethod(consts.HttpMethodPost).
		WithTimeout(3 * time.Second).
		WithResultType(reflect.TypeOf(Resp[map[string]*CenterUser]{})).
		WithContentType(consts.HttpContentTypeJson).
		Build()
	remoteService.RegisterCaller(consts.GetCenterUserByUIDs, getUserByUIDsCaller, []base.InterfaceFunc{common.AddJavaAuthSign(config.AppID, config.AppSecret)})

	// 获取健萌游客token
	getGymTokenCaller := base.NewHttpServiceCallerBuilder().
		WithUrl("ucenter/user/getSourceLogin").
		WithMethod(consts.HttpMethodPost).
		WithTimeout(3 * time.Second).
		WithResultType(reflect.TypeOf(Resp[string]{})).
		WithContentType(consts.HttpContentTypeJson).
		Build()
	remoteService.RegisterCaller(consts.GetGymToken, getGymTokenCaller, []base.InterfaceFunc{common.AddJavaAuthSign(config.AppID, config.AppSecret)})

	return remoteService
}
