package javacenter

import (
	"reflect"
	"time"

	"gitee.com/jianmengkeji/wego-common-pkg/src/consts"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote/base"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote/common"
)

var TagCenterService base.IRemoteService

type ServiceConfig struct {
	BaseURL   string `mapstructure:"base_url" json:"base_url" yaml:"base_url"`
	AppID     string `mapstructure:"app_id" json:"app_id" yaml:"app_id"`
	AppSecret string `mapstructure:"app_secret" json:"app_secret" yaml:"app_secret"`
}

func NewJavaCenterService(config *ServiceConfig) base.IRemoteService {
	remoteTagService := base.NewRemoteService(consts.JavaCenterService, config.BaseURL)
	uidIsInPackage := base.NewHttpServiceCallerBuilder().
		WithUrl("/openapi/humanPackage/uidIsInPackage").
		WithMethod(consts.HttpMethodPost).
		WithTimeout(3 * time.Second).
		WithResultType(reflect.TypeOf(Resp[bool]{})).
		WithContentType(consts.HttpContentTypeJson).
		Build()
	remoteTagService.RegisterCaller(consts.UidIsInPackage, uidIsInPackage, []base.InterfaceFunc{common.AddJavaAuthSign(config.AppID, config.AppSecret)})
	return remoteTagService
}
