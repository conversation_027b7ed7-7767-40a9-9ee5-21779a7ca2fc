package common

import (
	"context"
	"fmt"
	"gitee.com/jianmengkeji/wego-common-pkg/src/consts"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/log"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote/base"
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
	"go.uber.org/zap"

	"github.com/bytedance/sonic"
)

func AddJavaAuthSign(appID string, appSecret string) base.InterfaceFunc {
	return func(ctx context.Context, params ...interface{}) ([]interface{}, error) {
		for {
			if len(params) >= 3 {
				break
			}
			params = append(params, make(map[string]string))
		}
		jsBody := params[0]
		bodyBytes, err := sonic.Marshal(jsBody)
		if err != nil {
			log.AppLogger.Error("marshal param err:%s", zap.Error(err))
			return params, err
		}
		var header map[string]string
		if len(params) > 2 {
			var ok bool
			header, ok = params[2].(map[string]string)
			if !ok {
				log.AppLogger.Error("param type err, expect map[string]string, got %T", zap.Any("param", params[2]))
				return params, fmt.Errorf("param type err, expect map[string]string, got %T", params[1])
			}
		} else {
			header = make(map[string]string)
			if len(params) < 2 {
				params = append(params, make(map[string]string)) // 第二个参数为 url query
			}
			params = append(params, header) // 第三个参数为 header
		}
		query := params[1].(map[string]string)
		sign := utils.GenerateSign(query, string(bodyBytes), appSecret, "#")

		params[0] = bodyBytes // body
		header[consts.HeaderParamAppSign] = sign
		header[consts.HeaderParamAppKey] = appID
		header[consts.HeaderParamTraceID] = GetTraceID(ctx)
		params[2] = header // header
		return params, nil
	}
}

func GetTraceID(ctx context.Context) string {
	if ctx == nil {
		return ""
	}
	var traceID string
	traceIDi := ctx.Value(consts.TRACE_ID_KEY)
	if traceIDi != nil {
		traceID = traceIDi.(string)
	}
	return traceID
}
