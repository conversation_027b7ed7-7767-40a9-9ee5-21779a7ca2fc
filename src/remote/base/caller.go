package base

import (
	"bytes"
	"context"
	"errors"
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
	"github.com/spf13/cast"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"reflect"
	"time"

	"gitee.com/jianmengkeji/wego-common-pkg/src/consts"
	log "gitee.com/jianmengkeji/wego-common-pkg/src/pkg/log"
	"go.uber.org/zap"

	"github.com/bytedance/sonic"
)

type IServiceCaller interface {
	Call(ctx context.Context, params ...interface{}) (interface{}, error)

	SetService(service *Service)
}

type HttpServiceCallerBuilder struct {
	client      *http.Client
	timeout     time.Duration
	url         string
	method      string
	resultType  reflect.Type
	contentType consts.HttpContentType
}

func (b *HttpServiceCallerBuilder) WithTimeout(timeout time.Duration) *HttpServiceCallerBuilder {
	b.timeout = timeout
	return b
}

func (b *HttpServiceCallerBuilder) WithUrl(url string) *HttpServiceCallerBuilder {
	b.url = url
	return b
}

func (b *HttpServiceCallerBuilder) WithClient(client *http.Client) *HttpServiceCallerBuilder {
	b.client = client
	return b
}

func (b *HttpServiceCallerBuilder) WithMethod(method consts.HttpMethod) *HttpServiceCallerBuilder {
	b.method = method.String()
	return b
}

func (b *HttpServiceCallerBuilder) WithResultType(resultType reflect.Type) *HttpServiceCallerBuilder {
	b.resultType = resultType
	return b
}

func (b *HttpServiceCallerBuilder) WithContentType(contentType consts.HttpContentType) *HttpServiceCallerBuilder {
	b.contentType = contentType
	return b
}

func (b *HttpServiceCallerBuilder) Build() IServiceCaller {
	s := &httpServiceCaller{
		timeout:     b.timeout,
		url:         b.url,
		method:      b.method,
		resultType:  b.resultType,
		contentType: b.contentType,
	}
	if b.client == nil {
		s.client = &http.Client{
			Timeout: b.timeout,
		}
	}
	return s
}

func NewHttpServiceCallerBuilder() *HttpServiceCallerBuilder {
	return &HttpServiceCallerBuilder{}
}

type httpServiceCaller struct {
	client      *http.Client
	timeout     time.Duration
	url         string
	method      string
	contentType consts.HttpContentType
	resultType  reflect.Type

	service *Service
}

func (s *httpServiceCaller) SetService(service *Service) {
	s.service = service
}

// Call HTTP 接口调用
// params: 传入的参数，第一个参数为 body 中的参数
// 第二个参数为 url 中的 query 参数，类型为 map[string]string
// 第三个参数为 header 中的参数，类型为 map[string]string
// 随后的参数均为自定义参数，可在调用时传入
func (s *httpServiceCaller) Call(ctx context.Context, params ...interface{}) (interface{}, error) {
	if s.url == "" {
		log.AppLogger.Error("Url is empty")
		return nil, errors.New("caller url is empty")
	}
	var tmpUrl string
	if s.service != nil {
		tmp, joinPathErr := url.JoinPath(s.service.BaseURL, s.url)
		if joinPathErr != nil {
			log.AppLogger.Error("Join path failed",
				zap.Any("service_name", s.service.Name),
				zap.String("method", s.method),
				zap.String("url", s.url),
				zap.String("base_url", s.service.BaseURL),
				zap.Error(joinPathErr))
			return nil, joinPathErr
		}
		tmpUrl = tmp
	} else {
		tmpUrl = s.url
	}
	callURL, parseURLErr := url.Parse(tmpUrl)
	if parseURLErr != nil {
		log.AppLogger.Error("Parse url failed",
			zap.String("url", s.url),
			zap.String("tmp_url", tmpUrl),
			zap.String("service_name", s.service.Name),
			zap.String("method", s.method),
			zap.Error(parseURLErr))
		return nil, parseURLErr
	}

	if s.client == nil {
		s.client = &http.Client{
			Timeout: s.timeout,
		}
	}
	if s.method == "" {
		s.method = http.MethodGet
	}

	payload := &bytes.Buffer{}
	var contentType string
	switch s.contentType {
	case consts.HttpContentTypeJson:
		bodyBytes, marshalParamErr := s.getRequestBodyBytes(params)
		if marshalParamErr != nil {
			log.AppLogger.Error("Marshal param failed",
				zap.String("url", s.url),
				zap.String("service_name", s.service.Name),
				zap.String("method", s.method),
				zap.Error(marshalParamErr))
			return nil, marshalParamErr
		}
		payload.Write(bodyBytes)
		contentType = consts.HttpContentTypeJson.String()
	case consts.HttpContentTypeForm:
		var param interface{}
		if len(params) <= 0 {
			log.AppLogger.Error("Param is nil",
				zap.String("url", s.url),
				zap.String("service_name", s.service.Name),
				zap.String("method", s.method))
			return nil, errors.New("param is nil")
		}
		param = params[0]
		kv, ok := param.(map[string]string)
		if !ok {
			log.AppLogger.Error("Param is not map[string]string",
				zap.String("url", s.url),
				zap.String("service_name", s.service.Name),
				zap.String("method", s.method))
			return nil, errors.New("param is not map[string]string")
		}
		writer := multipart.NewWriter(payload)
		for k, v := range kv {
			writeErr := writer.WriteField(k, v)
			if writeErr != nil {
				log.AppLogger.Error("Write field failed",
					zap.String("url", s.url),
					zap.String("service_name", s.service.Name),
					zap.String("method", s.method),
					zap.Error(writeErr))
				return nil, writeErr
			}
		}
		defer writer.Close()
		contentType = writer.FormDataContentType()
	default:
		contentType = consts.HttpContentTypeJson.String()
	}

	httpReq, newRequestErr := http.NewRequestWithContext(ctx, s.method, callURL.String(), payload)
	if newRequestErr != nil {
		log.AppLogger.Error("Request failed",
			zap.String("url", s.url),
			zap.String("service_name", s.service.Name),
			zap.String("method", s.method),
			zap.Error(newRequestErr))

		return nil, newRequestErr
	}

	httpReq.Header.Add("Accept", "*/*")
	httpReq.Header.Add("Host", callURL.Host)
	httpReq.Header.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	httpReq.Header.Add("Connection", "keep-alive")
	httpReq.Header.Add("Content-Type", contentType)
	if len(params) > 1 && params[1] != nil {
		query, ok := params[1].(map[string]string)
		if !ok {
			log.AppLogger.Error("Query is not map[string]string",
				zap.String("url", s.url),
				zap.String("service_name", s.service.Name),
				zap.String("method", s.method))
			return nil, errors.New("query is not map[string]string")
		}
		if len(query) > 0 {
			queryValues := url.Values{}
			for k, v := range query {
				queryValues.Add(k, v)
			}
			httpReq.URL.RawQuery = queryValues.Encode()
		}
	}
	if len(params) > 2 {
		header, ok := params[2].(map[string]string)
		if !ok {
			log.AppLogger.Error("Header is not map[string]string",
				zap.String("url", s.url),
				zap.String("service_name", s.service.Name),
				zap.String("method", s.method))
			return nil, errors.New("header is not map[string]string")
		}
		for k, v := range header {
			httpReq.Header[k] = []string{v}
		}
	}

	httpResp, requestErr := s.client.Do(httpReq)

	if requestErr != nil {
		log.AppLogger.Error("Request failed",
			zap.String("url", s.url),
			zap.String("service_name", s.service.Name),
			zap.String("method", s.method),
			zap.Error(requestErr))
		return nil, requestErr
	}
	defer httpResp.Body.Close()

	body, ioErr := io.ReadAll(httpResp.Body)
	if ioErr != nil {
		log.AppLogger.Error("Read response body failed",
			zap.String("url", s.url),
			zap.String("service_name", s.service.Name),
			zap.String("method", s.method),
			zap.Error(ioErr))
		return nil, ioErr
	}

	var result interface{}
	if s.resultType != nil {
		respResult, unmarshalErr := utils.UnmarshalWithType(body, s.resultType)
		if unmarshalErr != nil {
			log.AppLogger.Error("Unmarshal response body failed",
				zap.String("url", s.url),
				zap.String("service_name", s.service.Name),
				zap.String("method", s.method),
				zap.Error(unmarshalErr))
			return nil, unmarshalErr
		}
		result = respResult
	} else {
		result = string(body)
	}
	return result, nil
}

func (s *httpServiceCaller) getRequestBodyBytes(params []interface{}) ([]byte, error) {
	if len(params) <= 0 {
		return []byte(""), nil
	}
	body := params[0]
	switch b := body.(type) {
	case []byte:
		return b, nil
	case string:
		return []byte(b), nil
	case int, int8, int32, int64, float32, float64, bool:
		return []byte(cast.ToString(b)), nil
	default:
		return sonic.Marshal(b)
	}
}
