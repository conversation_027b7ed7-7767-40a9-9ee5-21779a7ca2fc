package base

import (
	"context"
	"errors"
	"gitee.com/jianmengkeji/wego-common-pkg/src/consts"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/log"
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
	"go.uber.org/zap"
	"time"
)

type IRemoteService interface {
	RegisterCaller(serviceInterface consts.ServiceInterface, caller IServiceCaller, beforeInterfaceCall []InterfaceFunc)
	Call(ctx context.Context, serviceInterface consts.ServiceInterface, params ...interface{}) (interface{}, error)
}

type Service struct {
	Name    string
	BaseURL string
	Timeout time.Duration // 服务整体超时时间，如果 caller 中也设置了超时时间，会优先使用 caller 的超时设置
	// TODO 添加重试策略
	BeforeInterfaceCall map[consts.ServiceInterface][]InterfaceFunc
	InterfaceCaller     map[consts.ServiceInterface]IServiceCaller
}

func (s *Service) RegisterCaller(serviceInterface consts.ServiceInterface, caller IServiceCaller, beforeInterfaceCall []InterfaceFunc) {
	if s.InterfaceCaller == nil {
		s.InterfaceCaller = make(map[consts.ServiceInterface]IServiceCaller)
	}
	s.InterfaceCaller[serviceInterface] = caller
	if s.BeforeInterfaceCall == nil {
		s.BeforeInterfaceCall = make(map[consts.ServiceInterface][]InterfaceFunc)
	}
	s.BeforeInterfaceCall[serviceInterface] = beforeInterfaceCall
	caller.SetService(s)
}

func (s *Service) Call(ctx context.Context, serviceInterface consts.ServiceInterface, params ...interface{}) (interface{}, error) {
	caller, ok := s.InterfaceCaller[serviceInterface]
	if !ok {
		return nil, consts.RemoteServiceErrInterfaceNotRegistered
	}
	for _, f := range s.BeforeInterfaceCall[serviceInterface] {
		var err error
		params, err = f(ctx, params...)
		if err != nil {
			log.AppLogger.Error("remote service call err, interface before call err",
				zap.Error(err),
				zap.String("service_name", s.Name),
				zap.String("interface_name", serviceInterface.String()),
				zap.Any("params", params))
			return nil, consts.RemoteServiceErrInterfaceErr
		}
	}

	callResult, callErr := caller.Call(ctx, params...)
	if ctx.Err() != nil && errors.As(ctx.Err(), &context.DeadlineExceeded) {
		log.AppLogger.Error("remote service call err, interface timeout",
			zap.String("service_name", s.Name),
			zap.String("interface_name", serviceInterface.String()),
			zap.Any("params", params))
		return nil, consts.RemoteServiceErrInterfaceTimeout
	}
	if callErr != nil {
		log.AppLogger.Error("remote service call err, interface call err",
			zap.Error(callErr),
			zap.String("service_name", s.Name),
			zap.String("interface_name", serviceInterface.String()),
			zap.Any("params", params))
		return nil, consts.RemoteServiceErrInterfaceErr
	}
	log.AppLogger.Info("remote service call success",
		zap.String("service_name", s.Name),
		zap.String("interface_name", serviceInterface.String()),
		zap.Any("params", params),
		zap.Any("result", utils.MarshalToString(callResult)))
	return callResult, nil
}

func (s *Service) getTimeout(caller IServiceCaller) time.Duration {
	timeout := time.Second * 10 // 默认超时时间为 10s
	if s.Timeout > 0 {
		timeout = s.Timeout
	}

	switch t := caller.(type) {
	case *httpServiceCaller:
		if t.timeout > 0 {
			timeout = t.timeout
		}
	}
	return timeout
}

type RemoteServiceBuilder struct {
	name                consts.ServiceName
	baseURL             string
	timeout             time.Duration
	beforeInterfaceCall map[consts.ServiceInterface][]InterfaceFunc
	interfaceCaller     map[consts.ServiceInterface]IServiceCaller
}

func (b *RemoteServiceBuilder) WithName(name consts.ServiceName) *RemoteServiceBuilder {
	b.name = name
	return b
}

func (b *RemoteServiceBuilder) WithBaseURL(baseURL string) *RemoteServiceBuilder {
	b.baseURL = baseURL
	return b
}

func (b *RemoteServiceBuilder) WithTimeout(timeout time.Duration) *RemoteServiceBuilder {
	b.timeout = timeout
	return b
}

func (b *RemoteServiceBuilder) WithBeforeInterfaceCall(serviceInterface consts.ServiceInterface, funcs ...InterfaceFunc) *RemoteServiceBuilder {
	b.beforeInterfaceCall[serviceInterface] = funcs
	return b
}

func (b *RemoteServiceBuilder) WithInterfaceCaller(serviceInterface consts.ServiceInterface, caller IServiceCaller) *RemoteServiceBuilder {
	b.interfaceCaller[serviceInterface] = caller
	return b
}

func (b *RemoteServiceBuilder) Build() IRemoteService {
	return &Service{
		Name:                b.name.String(),
		BaseURL:             b.baseURL,
		Timeout:             b.timeout,
		BeforeInterfaceCall: b.beforeInterfaceCall,
		InterfaceCaller:     b.interfaceCaller,
	}
}

type InterfaceFunc func(ctx context.Context, params ...interface{}) ([]interface{}, error)

func NewRemoteService(name consts.ServiceName, baseURl string) IRemoteService {
	return &Service{
		Name:                name.String(),
		BaseURL:             baseURl,
		BeforeInterfaceCall: make(map[consts.ServiceInterface][]InterfaceFunc),
		InterfaceCaller:     make(map[consts.ServiceInterface]IServiceCaller),
	}
}
