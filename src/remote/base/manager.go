package base

import (
	"context"
	"time"

	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/log"
	"go.uber.org/zap"

	"gitee.com/jianmengkeji/wego-common-pkg/src/consts"
)

type RemoteServiceManager struct {
	remoteServices      map[consts.ServiceName]IRemoteService
	remoteServiceConfig map[consts.ServiceName]*RemoteServiceConfig
}

type RemoteServiceConfig struct {
	Timeout time.Duration
}

type ServiceConfigOption func(config *RemoteServiceConfig)

func (m *RemoteServiceManager) RegisterRemoteService(serviceName consts.ServiceName, service IRemoteService, options ...ServiceConfigOption) {
	m.remoteServices[serviceName] = service
	config := m.remoteServiceConfig[serviceName]
	if len(options) > 0 {
		if config == nil {
			config = &RemoteServiceConfig{}
		}
		for _, option := range options {
			option(config)
		}
		m.remoteServiceConfig[serviceName] = config
	}
}

func (m *RemoteServiceManager) GetRemoteService(serviceName consts.ServiceName) IRemoteService {
	return m.remoteServices[serviceName]
}

func (m *RemoteServiceManager) Call(ctx context.Context, serviceName consts.ServiceName, serviceInterface consts.ServiceInterface,
	params ...interface{},
) (interface{}, error) {
	service := m.GetRemoteService(serviceName)
	if service == nil {
		log.AppLogger.Error("service not register, serviceName:%s", zap.String("serviceName", serviceName.String()))
		return nil, consts.RemoteServiceErrServiceNotRegistered
	}
	now := time.Now()
	defer func() {
		log.AppLogger.Info("remote service call",
			zap.String("serviceName", serviceName.String()),
			zap.String("interfaceName", serviceInterface.String()),
			zap.Any("params", params),
			zap.Duration("cost", time.Since(now)))
	}()

	timeout := m.getTimeout(service, serviceInterface)
	if timeout > 0 {
		newCtx, cancel := context.WithTimeout(ctx, timeout)
		ctx = newCtx
		defer cancel()
	}
	return service.Call(ctx, serviceInterface, params...)
}

func (m *RemoteServiceManager) getTimeout(s IRemoteService, serviceInterface consts.ServiceInterface) time.Duration {
	switch st := s.(type) {
	case *Service:
		t := st.getTimeout(st.InterfaceCaller[serviceInterface])
		if t > 0 {
			return t
		}
		if m.remoteServiceConfig == nil {
			return 0
		}
		return m.remoteServiceConfig[consts.ServiceName(st.Name)].Timeout
	default:
		return 0
	}
}

var RemoteServiceMgr *RemoteServiceManager

func InitRemoteServiceManager() *RemoteServiceManager {
	RemoteServiceMgr = &RemoteServiceManager{
		remoteServices:      make(map[consts.ServiceName]IRemoteService),
		remoteServiceConfig: map[consts.ServiceName]*RemoteServiceConfig{},
	}
	return RemoteServiceMgr
}
