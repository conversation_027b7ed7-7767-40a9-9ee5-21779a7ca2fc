package consts

type WecomTagType int

func (w WecomTagType) String() string {
	switch w {
	case WecomTagTypeCorp:
		return "企业标签"
	case WecomTagTypeCustom:
		return "自定义标签"
	case WecomTagTypeRule:
		return "规则标签"
	}
	return ""
}

func (w WecomTagType) Int() int {
	return int(w)
}

const (
	WecomTagTypeCorp WecomTagType = iota + 1
	WecomTagTypeCustom
	WecomTagTypeRule
)

type WecomType string

func (w WecomType) String() string {
	return string(w)
}

func (w WecomType) ID() int {
	switch w {
	case WecomTypeDangHongBuRang:
		return 1
	case WecomTypeJianMeng:
		return 2
	}
	return -1
}

func (w WecomType) GetCorpID() string {
	switch w {
	case WecomTypeDangHongBuRang:
		return "wwbdeaa3266417c5e1"
	case WecomTypeJianMeng:
		return "wwd3b3c4f7f7f7f7f7" // TODO 等健萌接入后，修改为健萌的corpID
	}
	return ""
}

func (w WecomType) GetBusinessType() BusinessType {
	switch w {
	case WecomTypeDangHongBuRang:
		return BusinessTypeBetterWe
	case WecomTypeJianMeng:
		return BusinessTypeGYM3
	}
	return -1
}

func (w WecomType) GetMappingAgent() WecomAgent {
	switch w {
	case WecomTypeDangHongBuRang:
		return WecomAgentCRMJianZhiYing
	default:
		return ""
	}
}

func WecomTypeFromWecomTypeID(id int) WecomType {
	switch id {
	case 1:
		return WecomTypeDangHongBuRang
	case 2:
		return WecomTypeJianMeng
	}
	return ""
}

const (
	WecomTypeDangHongBuRang WecomType = "当红不让"
	WecomTypeJianMeng       WecomType = "健萌"
)

type WecomAgent string

func (w WecomAgent) String() string {
	return string(w)
}

func (w WecomAgent) GetWecomType() WecomType {
	switch w {
	case WecomAgentCRMJianZhiYing:
		return WecomTypeDangHongBuRang
	}
	return ""
}

const (
	WecomAgentCRMJianZhiYing WecomAgent = "减脂营CRM"
)
