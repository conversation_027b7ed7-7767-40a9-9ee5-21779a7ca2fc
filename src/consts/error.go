package consts

type ICustomError interface {
	GetErrorCode() int
	GetErrorMsg() string
}

type CustomError struct {
	ErrorCode int
	ErrorMsg  string
}

func (c CustomError) Error() string {
	return c.ErrorMsg
}

func (c CustomError) GetErrorMsg() string {
	return c.ErrorMsg
}

func (c CustomError) GetErrorCode() int {
	return c.ErrorCode
}

type CustomErrors struct {
	BusinessError CustomError
	ValidateError CustomError
	TokenError    CustomError
	PanicError    CustomError
}

var Errors = CustomErrors{
	ValidateError: CustomError{10000, "请求参数错误"},
	TokenError:    CustomError{10100, "服务鉴权失效"},
	PanicError:    CustomError{10200, "服务器内部错误"},
	BusinessError: CustomError{20000, "业务错误"},
}

func NewBusinessError(errorCode int, msg string) *CustomError {
	return &CustomError{
		ErrorCode: errorCode,
		ErrorMsg:  msg,
	}
}

const (
	ParamValidateCommonErr = "Parameter error"
)

var (
	BusinessAuthErr = NewBusinessError(10003, "业务鉴权错误，请检查参数或者签名是否正确")

	RemoteServiceErrServiceNotRegistered   = NewBusinessError(30000, "服务未注册")
	RemoteServiceErrInterfaceNotRegistered = NewBusinessError(30001, "服务接口未注册")
	RemoteServiceErrInterfaceTimeout       = NewBusinessError(30002, "服务接口超时")
	RemoteServiceErrInterfaceErr           = NewBusinessError(30003, "服务接口调用错误")
)
