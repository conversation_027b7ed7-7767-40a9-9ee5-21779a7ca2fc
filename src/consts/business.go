package consts

type BusinessType int

const (
	BusinessTypeBetterWe     BusinessType = 1
	BusinessTypeGYM3         BusinessType = 2
	BusinessTypeShoppingMall BusinessType = 3
)

func (b BusinessType) Int() int {
	return int(b)
}

func (b BusinessType) Name() string {
	switch b {
	case BusinessTypeBetterWe:
		return "BetterWe"
	case BusinessTypeGYM3:
		return "健萌"
	case BusinessTypeShoppingMall:
		return "商城"
	}
	return "unknown"
}

func (b BusinessType) GetWecomType() WecomType {
	switch b {
	case BusinessTypeBetterWe:
		return WecomTypeDangHongBuRang
	case BusinessTypeGYM3:
		return WecomTypeJianMeng
	case BusinessTypeShoppingMall:
		return WecomTypeDangHongBuRang
	}
	return ""
}
