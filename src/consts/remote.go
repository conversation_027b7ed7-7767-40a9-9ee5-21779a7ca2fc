package consts

// ServiceName 定义服务名称的类型，基于字符串。
type ServiceName string

// String 方法将 ServiceName 类型转换为字符串。
func (s ServiceName) String() string {
	return string(s)
}

// 常量定义了系统中使用的服务名称。
const (
	// UserCenterService 表示用户中心服务。
	UserCenterService ServiceName = "UserCenterService"

	// TagCenterService 表示标签中心服务。
	JavaCenterService ServiceName = "JavaCenterService"

	// WecomTagService 表示企业微信标签服务。
	WecomTagService ServiceName = "WecomTag"

	// OpenapiService 表示开放 API 服务。
	OpenapiService ServiceName = "OpenAPI"
)

// ServiceInterface 定义服务接口的类型，基于字符串。
type ServiceInterface string

// String 方法将 ServiceInterface 类型转换为字符串。
func (s ServiceInterface) String() string {
	return string(s)
}

// 常量定义了系统中使用的具体服务接口。
const (
	// GetCenterUserBySourceUIDs 获取通过来源 UID 映射的用户中心数据。
	GetCenterUserBySourceUIDs ServiceInterface = "GetCenterUserBySourceUIDs"
	// GetCenterUserByUIDs 获取通过用户中心 UID 映射的用户数据。
	GetCenterUserByUIDs ServiceInterface = "GetCenterUserByUIDs"
	// GetGymVistorToken 获取健萌游客访问令牌。
	GetGymToken ServiceInterface = "GetGymToken"

	// UidIsInPackage 判断 UID 是否在指定包中。
	UidIsInPackage ServiceInterface = "UidIsInPackage"

	// GetWecomAccessToken 获取企业微信访问令牌。
	GetWecomAccessToken ServiceInterface = "GetWecomAccessToken"
	// RefreshWecomAccessToken 刷新企业微信访问令牌。
	RefreshWecomAccessToken ServiceInterface = "RefreshWecomAccessToken"

	// GetCorpTagList 获取企业标签列表。
	GetCorpTagList ServiceInterface = "GetCorpTagList"
	// GetCorpUserTagList 获取企业用户的标签列表。
	GetCorpUserTagList ServiceInterface = "GetCorpUserTagList"
	// AddCorpTag 添加企业标签。
	AddCorpTag ServiceInterface = "AddCorpTag"
	// EditCorpTag 编辑企业标签。
	EditCorpTag ServiceInterface = "EditCorpTag"
	// DeleteCorpTag 删除企业标签。
	DeleteCorpTag ServiceInterface = "DeleteCorpTag"
	// MarkUserTag 标记用户标签。
	MarkUserTag ServiceInterface = "MarkUserTag"
)

// 常量定义了 HTTP 请求头中使用的参数名称。
const (
	// HeaderParamTraceID 表示请求跟踪 ID 的头部参数。
	HeaderParamTraceID = "X-TRACEID"
	// HeaderParamAppKey 表示应用密钥的头部参数。
	HeaderParamAppKey = "X-APPKEY"
	// HeaderParamAppSign 表示签名验证的头部参数。
	HeaderParamAppSign = "X-SIGN"
	// HeaderParamSyncTag 表示同步标记的头部参数。
	HeaderParamSyncTag = "X-SYNC-TAG"
)
