# wego-common-pkg

1. 基本介绍
    1.1. wego-common-pkg 是一个通用的公共包，封装了常用功能，方便开发人员使用。

2. 使用说明
    * golang 版本要求 >= 1.22.5
    * IDE推荐：Goland

3. 3.1 目录结构
```
├───src                 // 项目代码
│   ├───app             // 框架核心代码
│   ├───consts          // 常量定义
│   ├───middleware      // 中间件
│   ├───pkg             // 通用包
│   │   ├───cache       // 缓存
│   │   │   ├───local   // 本地缓存
│   │   │   └───redis   // redis缓存
│   │   ├───cron        // 定时任务
│   │   ├───db          // 数据库
│   │   │   └───mysql   // mysql
│   │   ├───log         // 日志
│   │   ├───mq          // 消息队列
│   │   │   ├───base    // 消息队列基础封装
│   │   │   └───rabbitmq   // rabbitmq
│   ├───remote          // 外部服务
│   │   ├───base        // 基础封装
│   │   ├───common      // 公共服务
│   │   ├───datacenter  // 用户中心
│   │   ├───openapi     // 开放平台
│   │   └───wecom       // 企业微信
│   └───utils           // 工具类
└───test                // 测试代码
```

4. 目前已接入功能组件
    * redis 缓存
    * mysql 数据库
    * zapLog 日志
    * alarm 钉钉预警
    * gocache 本地缓存
    * rabbitmq 消息队列
    * 全链路日志追踪

5. 开发规范
    1. 具体项目结构可以参考test/demo 新起项目直接可以复制demo过去
    2. 所有路由都写在router文件夹下,不用额外再去注册路由,程序初始化时候会自动注册路由
    3. 控制器层只做参数校验，业务逻辑全部在service层做.
    4. Service层必须实现接口，并且实现接口中的方法,结构体统一在Service初始化,减少内存消耗

6. 示例代码
    1. 路由注册
```go
1. 在controller下新建路由文件，一个文件即是一个路由组
// 所有路由文件必须有注册方法, 同时在此处注册中间件以及限流。
func RegisterKeepAliveRouter(r *gin.Engine, manager *utils.RouteLimiterManager, cbManager *utils.CircuitBreakerManager) {
	// 注册中间件
	r.Use(
		middleware.Cors(),
		middleware.GinLogger(),
		middleware.Recover(),
		middleware.AuthMiddleware(),
		middleware.RateLimiterMiddleware(manager),
		middleware.CircuitBreakerMiddleware(cbManager),
	)

	userGroup := r.Group("/v1")
	{
		userGroup.GET("/keepalive", keepalive)
		userGroup.GET("/keepalive2", keepalive)
		userGroup.GET("/keepalive3", keepaliveError)
	}

	// 为特定路由设置限流规则
	err := manager.SetRouteLimiter("/v1", "5-M") // 特定路由限流：每分钟允许 5 次请求
	if err != nil {
		log.Fatalf("Failed to set route limiter: %v", err)
	}

}

// 具体实现方法 这里只能接收参数，以及参数校验，不能做任何的业务逻辑
func keepalive(c *gin.Context) {
	unify_id, _ := c.Get("unify_id")
	app.SetSuccess(c, unify_id)
}

func keepaliveError(c *gin.Context) {
	alarm.DingDingNormal("服务触发熔断, 请尽快排查问题", "服务触发熔断")
	app.SetErrorBreaker(c, "breaker test", 200)
}


```
   2. Service代码规范示例
```go
package v1

import (
	"context"
)

// 每个service必须定义接口
type IHealthService interface {
	Health(ctx context.Context) string
}

// 统一生成结构体实例
var HealthService IHealthService = &healthService{}

type healthService struct{}

func (s *healthService) Health(ctx context.Context) string {
	// 系统错误 alarm.DingDingNormal("测试实施")
	// 自定义报警 alarm.CustomAlarm("自定义报警")
	// alarm.CustomAlarm("https://oapi.dingtalk.com/robot/send?access_token=a01da9fa6fa3bbad9812ae54bd9a21fe938b2dd502ad24090ff0e57162cdceb5", "dsadsa")
	return "OK"
}
```
   3.生产者消费者代码示例
```go
消费者创建在consumer目录下
生产者创建在producer目录下
消息传递中的结构体定义在model目录下
上面三者需一一对应
```

7. TODO列表
    - [x] 全局统一返回以及错误处理
    - [x] 全链路日志追踪
    - [x] Mysql
    - [x] Redis
    - [x] zap Log
    - [x] Alarm错误预警
    - [x] 本地缓存gocache
    - [x] RabbitMQ
    - [x] JWT鉴权
    - [x] API接口限流
    - [x] 熔断组件

